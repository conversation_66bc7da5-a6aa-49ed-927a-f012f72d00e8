import { 
  type User, 
  type InsertUser, 
  type Booking, 
  type InsertBooking,
  type Service,
  type ServiceCategory,
  type IStorage
} from "@shared/schema";

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private bookings: Map<string, Booking>;
  private services: Service[];
  private serviceCategories: ServiceCategory[];
  currentId: number;

  constructor() {
    this.users = new Map();
    this.currentId = 1;
    this.bookings = new Map();
    
    // Initialize with some default services
    this.services = [
      { id: 1, name: "Trattamento viso specifico e massaggio", price: "50.00", category: "viso", description: "Trattamento completo per il viso", duration: 60, active: true },
      { id: 2, name: "Trattamento viso alla bava di lumaca", price: "35.00", category: "viso", description: "Trattamento idratante", duration: 45, active: true },
      { id: 3, name: "Pulizia del viso classica", price: "30.00", category: "viso", description: "Pulizia profonda", duration: 45, active: true },
      { id: 4, name: "Pedicure con trattamento callosità", price: "25.00", category: "manicure", description: "Trattamento completo", duration: 40, active: true },
      { id: 5, name: "Manicure", price: "15.00", category: "manicure", description: "Manicure classica", duration: 30, active: true },
      { id: 6, name: "Cera completa", price: "25.00", category: "epilazione", description: "Epilazione completa", duration: 60, active: true },
      { id: 7, name: "Laminazione ciglia", price: "40.00", category: "ciglia", description: "Laminazione completa", duration: 45, active: true },
    ];
    
    // Initialize with some default categories
    this.serviceCategories = [
      { id: 1, name: "Trattamenti Viso", displayOrder: 1 },
      { id: 2, name: "Manicure e Pedicure", displayOrder: 2 },
      { id: 3, name: "Epilazione", displayOrder: 3 },
      { id: 4, name: "Sopracciglia e Ciglia", displayOrder: 4 },
    ];
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }
  
  // Booking methods
  async getBookings(): Promise<Booking[]> {
    return Array.from(this.bookings.values());
  }
  
  async getBookingById(id: string): Promise<Booking | undefined> {
    return this.bookings.get(id);
  }
  
  async createBooking(booking: InsertBooking): Promise<Booking> {
    const now = new Date();
    const newBooking: Booking = {
      ...booking,
      prezzoTotale: String(booking.prezzoTotale),
      note: booking.note || null,
      synced: booking.synced || false,
      createdAt: now
    };
    this.bookings.set(booking.id, newBooking);
    return newBooking;
  }
  
  async updateBooking(id: string, booking: Partial<InsertBooking>): Promise<Booking> {
    const existingBooking = this.bookings.get(id);
    if (!existingBooking) {
      throw new Error(`Booking with ID ${id} not found`);
    }
    
    const updatedBooking: Booking = {
      ...existingBooking,
      ...(booking.prezzoTotale !== undefined ? { prezzoTotale: String(booking.prezzoTotale) } : {}),
      ...(booking.nome !== undefined ? { nome: booking.nome } : {}),
      ...(booking.cognome !== undefined ? { cognome: booking.cognome } : {}),
      ...(booking.email !== undefined ? { email: booking.email } : {}),
      ...(booking.telefono !== undefined ? { telefono: booking.telefono } : {}),
      ...(booking.operatrice !== undefined ? { operatrice: booking.operatrice } : {}),
      ...(booking.dataAppuntamento !== undefined ? { dataAppuntamento: booking.dataAppuntamento } : {}),
      ...(booking.oraAppuntamento !== undefined ? { oraAppuntamento: booking.oraAppuntamento } : {}),
      ...(booking.servizi !== undefined ? { servizi: booking.servizi } : {}),
      ...(booking.note !== undefined ? { note: booking.note || null } : {}),
      ...(booking.synced !== undefined ? { synced: booking.synced } : {})
    };
    
    this.bookings.set(id, updatedBooking);
    return updatedBooking;
  }
  
  async deleteBooking(id: string): Promise<void> {
    this.bookings.delete(id);
  }
  
  // Service methods
  async getServices(): Promise<Service[]> {
    return this.services.filter(service => service.active);
  }
  
  async getServicesByCategory(category: string): Promise<Service[]> {
    return this.services.filter(service => service.category === category && service.active);
  }
  
  async getServiceCategories(): Promise<ServiceCategory[]> {
    return [...this.serviceCategories].sort((a, b) => a.displayOrder - b.displayOrder);
  }
  
  // Time slots methods
  async getAvailableTimeSlots(date: string): Promise<string[]> {
    // In a real application, this would check bookings and return available slots
    // For now, return default time slots
    return [
      "09:00", "09:30", "10:00", "10:30", "11:00", "11:30", 
      "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00"
    ];
  }
}

export const storage = new MemStorage();
