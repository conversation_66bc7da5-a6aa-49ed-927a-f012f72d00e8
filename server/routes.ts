import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { Booking, insertBookingSchema } from "@shared/schema";
import * as airtableService from "./airtable-service";

export async function registerRoutes(app: Express): Promise<Server> {
  // All API routes prefixed with /api

  // Get all bookings
  app.get('/api/bookings', async (req, res) => {
    try {
      const bookings = await storage.getBookings();
      res.json(bookings);
    } catch (error) {
      console.error('Error fetching bookings:', error);
      res.status(500).json({ error: 'Failed to fetch bookings' });
    }
  });

  // Get a specific booking by ID
  app.get('/api/bookings/:id', async (req, res) => {
    try {
      const booking = await storage.getBookingById(req.params.id);

      if (!booking) {
        return res.status(404).json({ error: 'Booking not found' });
      }

      res.json(booking);
    } catch (error) {
      console.error(`Error fetching booking ${req.params.id}:`, error);
      res.status(500).json({ error: 'Failed to fetch booking' });
    }
  });

  // Create a new booking
  app.post('/api/bookings', async (req, res) => {
    try {
      // Validate request body against schema
      const validatedData = insertBookingSchema.parse(req.body);

      // Salva nel database locale
      const booking = await storage.createBooking(validatedData);

      // Se Airtable è configurato, tenta di salvare anche lì
      if (airtableService.isAirtableConfigured()) {
        try {
          await airtableService.createBooking(validatedData);

          // Aggiorna lo stato di sincronizzazione
          await storage.updateBooking(booking.id, { synced: true });
        } catch (airtableError) {
          console.error('Error syncing to Airtable:', airtableError);
          // Continua comunque, abbiamo già salvato localmente
        }
      }

      // Return the created booking
      res.status(201).json(booking);
    } catch (error) {
      console.error('Error creating booking:', error);

      if (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError') {
        return res.status(400).json({ error: 'Invalid booking data', details: (error as any).errors });
      }

      res.status(500).json({ error: 'Failed to create booking' });
    }
  });

  // Update an existing booking
  app.patch('/api/bookings/:id', async (req, res) => {
    try {
      const bookingId = req.params.id;

      // Check if booking exists
      const existingBooking = await storage.getBookingById(bookingId);

      if (!existingBooking) {
        return res.status(404).json({ error: 'Booking not found' });
      }

      // Validate request body against schema
      const validatedData = insertBookingSchema.partial().parse(req.body);

      // Update booking in storage
      const updatedBooking = await storage.updateBooking(bookingId, validatedData);

      // Return the updated booking
      res.json(updatedBooking);
    } catch (error) {
      console.error(`Error updating booking ${req.params.id}:`, error);

      if (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError') {
        return res.status(400).json({ error: 'Invalid booking data', details: (error as any).errors });
      }

      res.status(500).json({ error: 'Failed to update booking' });
    }
  });

  // Delete a booking
  app.delete('/api/bookings/:id', async (req, res) => {
    try {
      const bookingId = req.params.id;

      // Check if booking exists
      const existingBooking = await storage.getBookingById(bookingId);

      if (!existingBooking) {
        return res.status(404).json({ error: 'Booking not found' });
      }

      // Delete booking from storage
      await storage.deleteBooking(bookingId);

      // Return success response
      res.status(204).end();
    } catch (error) {
      console.error(`Error deleting booking ${req.params.id}:`, error);
      res.status(500).json({ error: 'Failed to delete booking' });
    }
  });

  // Get available time slots for a specific date
  app.get('/api/timeslots', async (req, res) => {
    try {
      const { date } = req.query;

      if (!date || typeof date !== 'string') {
        return res.status(400).json({ error: 'Date parameter is required' });
      }

      // Get available time slots
      const timeSlots = await storage.getAvailableTimeSlots(date);

      res.json(timeSlots);
    } catch (error) {
      console.error('Error fetching time slots:', error);
      res.status(500).json({ error: 'Failed to fetch time slots' });
    }
  });

  // Get all services - Usa sempre i dati statici
  app.get('/api/services', async (req, res) => {
    try {
      console.log('API request received for /api/services');

      // Usa sempre i servizi statici dal file JSON
      const services = await airtableService.getServices();
      console.log(`Retrieved ${services.length} services from static JSON`);
      return res.json(services);
    } catch (error) {
      console.error('Error fetching services:', error);
      res.status(500).json({ error: 'Errore durante il recupero dei servizi' });
    }
  });

  // Get services by category
  app.get('/api/services/category/:category', async (req, res) => {
    try {
      const { category } = req.params;
      const services = await storage.getServicesByCategory(category);
      res.json(services);
    } catch (error) {
      console.error(`Error fetching services for category ${req.params.category}:`, error);
      res.status(500).json({ error: 'Failed to fetch services by category' });
    }
  });

  // Add a new route to get service categories
  app.get('/api/service-categories', async (req, res) => {
    try {
      console.log('API request received for /api/service-categories');

      // Usa sempre le categorie statiche
      const categories = await airtableService.getServiceCategories();
      console.log(`Retrieved ${categories.length} categories from static data`);
      return res.json(categories);
    } catch (error) {
      console.error('Error fetching service categories:', error);
      res.status(500).json({ error: 'Errore durante il recupero delle categorie' });
    }
  });

  // Autenticazione cliente
  app.post('/api/auth/login', async (req, res) => {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ error: 'Email è richiesta' });
      }

      // Se Airtable è configurato, usa quello per l'autenticazione
      if (airtableService.isAirtableConfigured()) {
        const client = await airtableService.authenticateClient(email);

        if (client) {
          return res.json({ success: true, client });
        } else {
          return res.status(404).json({ error: 'Cliente non trovato' });
        }
      }

      // Fallback: autenticazione locale semplificata
      res.json({
        success: true,
        client: {
          email,
          nome: 'Cliente',
          cognome: 'Test',
          id: 'local_' + Date.now()
        }
      });
    } catch (error) {
      console.error('Error during authentication:', error);
      res.status(500).json({ error: 'Errore durante l\'autenticazione' });
    }
  });

  // Trova o crea cliente
  app.post('/api/clients/find-or-create', async (req, res) => {
    try {
      const clientData = req.body;

      if (!clientData.email) {
        return res.status(400).json({ error: 'Email è richiesta' });
      }

      // Se Airtable è configurato, usa quello
      if (airtableService.isAirtableConfigured()) {
        const client = await airtableService.findOrCreateClient(clientData);
        return res.json(client);
      }

      // Fallback: creazione locale
      res.json({
        id: 'local_' + Date.now(),
        fields: {
          Nome: clientData.nome,
          Cognome: clientData.cognome,
          Email: clientData.email,
          Telefono: clientData.telefono
        }
      });
    } catch (error) {
      console.error('Error finding/creating client:', error);
      res.status(500).json({ error: 'Errore durante la gestione del cliente' });
    }
  });

  // Test connessione Airtable
  app.get('/api/test/airtable', async (req, res) => {
    try {
      const result = await airtableService.testAirtableConnection();
      res.json(result);
    } catch (error) {
      console.error('Error testing Airtable connection:', error);
      res.status(500).json({
        success: false,
        error: 'Errore durante il test di connessione'
      });
    }
  });

  // Recupera le prenotazioni di un cliente
  app.get('/api/appointments/client/:identifier', async (req, res) => {
    try {
      const { identifier } = req.params;

      if (!identifier) {
        return res.status(400).json({ error: 'Identificatore cliente richiesto' });
      }

      // Se Airtable è configurato, usa quello
      if (airtableService.isAirtableConfigured()) {
        const appointments = await airtableService.getClientAppointments(identifier);
        return res.json(appointments);
      }

      // Fallback: nessuna prenotazione locale
      res.json([]);
    } catch (error) {
      console.error('Error fetching client appointments:', error);
      res.status(500).json({ error: 'Errore durante il recupero delle prenotazioni' });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
