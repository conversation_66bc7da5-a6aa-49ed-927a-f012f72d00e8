import fetch from 'node-fetch';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Carica le variabili d'ambiente
dotenv.config();

// Configurazione API Airtable
const AIRTABLE_CONFIG = {
  baseId: 'appbZ4ONNnQp69VtM', // Base ID dal codice Python
  apiUrl: 'https://api.airtable.com/v0',
  apiKey: process.env.AIRTABLE_API_KEY || '',
  appointmentsTable: 'tblAanKl9ORXkyFEr', // Tabella appuntamenti
  clientsTable: 'tbluoomRrGPKhjQPP' // Tabella clienti - ID corretto
};

// Verifica se la configurazione Airtable è valida
export function isAirtableConfigured(): boolean {
  return !!AIRTABLE_CONFIG.apiKey;
}

// Aggiungo l'interfaccia per i servizi
interface ServiceMap {
  [key: string]: string;
}

// Modifico la dichiarazione della mappa dei servizi
const servicesMap: ServiceMap = {
  "Baffetto": "recoyhBAnENtdKJKb",
  "Baffetto e sopracciglia": "recZ2H220kkZMjsjc",
  "Bendaggi": "recYnYCbS1Sbfudcp",
  "Bendaggi + massaggio 30 min.": "recc1jIlHQszTGImK",
  "Cera ascelle": "recw9fXgtMUYysOlG",
  "Cera braccia": "recNMIx4C7q16vwZa",
  "Cera completa": "rec5O9ax87p6WoM16",
  "Cera coscia": "recMGDSPcEZPbNckM",
  "Cera gambaletto": "recIART7UyBoRz12D",
  "Cera inguine": "recvV4t6fWrZIrsgg",
  "Cera inguine integrale": "rec63EZFBVJT406UA",
  "Cera pancia": "recHqyJoPWc2saqJO",
  "Cera schiena": "recqsHxB90G8Tqzle",
  "Cera uomo addome/schiena": "rechKd7VOtRfx9Q5D",
  "Copertura in gel": "recpmt2KsEF0hZ5r3",
  "Endospheres": "recJhDTWoYZ44ZyJJ",
  "Henné sopracciglia": "recNjUOKs0uICJyPi",
  "Laminazione ciglia": "recd0pxrTh7zTd2eC",
  "Laminazione sopracciglia": "recC6A1EEqr9rg1np",
  "Laser addome completo": "recKIF68UWkubwJ2G",
  "Laser addome zona Alba": "recoU9ezv1XBoJNLM",
  "Laser ascelle": "recoZUibF4OtirMqh",
  "Laser baffetto": "recClSxQikAQxM5ll",
  "Laser basette": "recrlJbo67FWXmH1Q",
  "Laser braccia": "rec69WdeljCrVcz8R",
  "Laser coscia": "reckiYTzS5yyRsGGs",
  "Laser gambaletto": "recMiQQleyHpAqMy1",
  "Laser gambe": "rec6z8qZvV53dpgmK",
  "Laser inguine": "rec3sjcpErfP6BVzb",
  "Laser mento": "recYVFSExrYGO8rQs",
  "Laser schiena completa": "recTbVCoBeDDM2BZO",
  "Laser schiena parte bassa": "rectFlwk9bNSTNUAQ",
  "Laser viso completo": "recR4zHxp0a7jNB71",
  "Manicure": "recDy8Io82XLXxqyR",
  "Massaggio con metodo Renata Franca": "reczaWgQYQzp3ygej",
  "Massaggio decontratturante": "reci91gcEp70C1wVX",
  "Massaggio linfodrenante 30 min.": "recfDVZXDLfEcNS5o",
  "Massaggio linfodrenante 50 min.": "rec3CAxOkKsBT6cx6",
  "Massaggio relax": "recnPO1iuPIrFdxaV",
  "Massaggio viso": "recfvajiXGh9KTvgY",
  "Pedicure combi": "recDbENzLPsUfWbpD",
  "Pedicure con semipermanente": "rec167gNnG2X02E1J",
  "Pedicure con trattamento callosità": "rec4ZTjDJsjR5dfz0",
  "Pedicure curativo": "recEnFGqa7vArVEcu",
  "Pedicure estetico": "recmySGmn8z3Wk11g",
  "Peeling viso": "recVx8ohpxu8KUy41",
  "Pressoterapia": "receZoJobMUHDRU4B",
  "Pressoterapia + massaggio 30 min.": "rec1jWh7AtOEvcDmY",
  "Pressoterapia + massaggio  50 min.": "recXcknC7s81YoSJr",
  "Pulizia del viso classica": "recA0BKUGgHnUQovX",
  "Pulizia del viso con macchinari": "reco4oInNFaaQASiG",
  "Radiofrequenza viso": "recyEIgfjgn3tvauF",
  "Radiofrequenza addome, cosce, glutei": "recoaO5GmxGnX10R4",
  "Ricostruzione": "recgv2pR35BmBg1JA",
  "Scrub braccia": "reclmqyEOh3Cmk1tE",
  "Scrub corpo": "rec7R08t0w4fToQKv",
  "Scrub gambe": "recIB1MSDRviqqrDZ",
  "Semipermanente clássico": "rec56wFOJzHz6VwKO",
  "Semipermanente combi": "recQwG5RLQyCDieBG",
  "Semipermanente piedi": "reczMpPIfLB4lCZwr",
  "Sopracciglia": "recp2rtw6mGl3VFTG",
  "Trattamento argilla viso": "recvuAxVQWozpOuZo",
  "Trattamento bava di lumaca": "rec0rsOBTK7XINkQ7",
  "Trattamento spirulina viso": "recEZx31j2LAbwetD",
  "Trattamento viso specifico + massaggio": "recKrRoE2jgVyebOF",
  "Rimozione semipermanente": "rec5okTbjBHBiCysv",
  "Consulenza viso/corpo": "recPKaB87XewLJ9BZ",
  "Cambio smalto": "recgLPs9bN0SNdF56",
  "Endospheres addome": "recosZKizeECRoHcD",
  "Rimozione semipermanente piedi": "reckwvjdLVae1hw9R",
  "Cambio smalto piedi": "rec2JzTu1unZu1BR9",
  "Consulenza laser gratuita": "recz7QaMCdPZPrw2Q",
  "Trattamento all'alga corpo": "rec8VGAWP0VuN89oH",
  "NATUROPATA - Tuina": "recZOquTLxgygipZc",
  "NATUROPATA - Shiatsu": "recCyt3Cpe8Rjo7Es",
  "NATUROPATA - Riflessologia plantare": "recBiUPIpVOjzQe7k",
  "MAKE-UP artist - Trucco": "recsFnHjuK7fHYtsQ",
  "LASHMAKER - Extencion ciglia": "rec1c65RlGX0kFUGj",
  "Riparazione nails": "recoXzgfP550AkgQQ",
  "Ricostruzione 2 unghie": "recMEszfqOeWpI8cV",
  "Cera gambe": "rectOiKW4SdPtIcjE",
  "Magneto Cube EVO": "recxTI1Y5xBTDcvYd",
  "Hilift Kube": "recviUsF6V084IlTj",
  "Controllo laser": "recihtXBahcyoZYjx",
  "Ricostruzione Instanails": "rec1fhZwjvgut0aEa"
};

// Aggiungo le interfacce per le risposte di Airtable
interface AirtableError {
  error: {
    message: string;
  };
}

interface AirtableResponse {
  records: Array<{
    id: string;
    fields: Record<string, unknown>;
  }>;
}

// Funzione per formattare i dati per Airtable
function formatDataForAirtable(data: any) {
  // Mappa i campi del form ai campi di Airtable
  return {
    "fields": {
      "Nome": data.nome,
      "Cognome": data.cognome,
      "Email": data.email,
      "Telefono": data.telefono,
      "Operatrice": data.operatrice,
      "Data Appuntamento": data.dataAppuntamento,
      "Ora Appuntamento": data.oraAppuntamento,
      "Servizi": data.servizi.map((s: any) => s.name).join(', '),
      "Prezzo Totale": data.prezzoTotale,
      "Note": data.note || '',
      "Stato": "Confermato"
    }
  };
}

// Modifico la funzione createBooking per gestire correttamente i tipi
export async function createBooking(bookingData: {
  nome: string;
  cognome: string;
  email: string;
  telefono: string;
  servizi: string[];
  dataAppuntamento: string;
  oraAppuntamento: string;
  operatrice?: string;
}) {
  if (!isAirtableConfigured()) {
    throw new Error('Airtable API non configurata');
  }

  try {
    // Verifica se il cliente esiste
    const clienteEsiste = await findOrCreateClient({
      nome: bookingData.nome,
      cognome: bookingData.cognome,
      email: bookingData.email,
      telefono: bookingData.telefono
    });

    // Prepara i servizi con i loro ID
    const serviziIds = bookingData.servizi
      .map(servizio => servicesMap[servizio] || '')
      .filter(id => id !== '');

    // Usa i nomi dei campi esatti dalla documentazione Airtable
    const fields = {
      "NOME DA APP": bookingData.nome,
      "COGNOME DA APP": bookingData.cognome,
      "Servizi": serviziIds,
      "SERVIZI DA APP": bookingData.servizi,
      "Data Appuntamento": `${bookingData.dataAppuntamento}T${bookingData.oraAppuntamento}:00.000Z`,
      "Operatrici": bookingData.operatrice || "Francesca Cotugno",
      "STATO": "RICHIESTO",
      "TIPO APPUNTAMENTO": "STANDARD"
    };

    const payload = {
      records: [{ fields }]
    };

    const url = `https://api.airtable.com/v0/${AIRTABLE_CONFIG.baseId}/tblAanKl9ORXkyFEr`;
    const headers = {
      'Authorization': `Bearer ${AIRTABLE_CONFIG.apiKey}`,
      'Content-Type': 'application/json'
    };

    console.log('=== HTTP REQUEST ===');
    console.log('Method: POST');
    console.log('URL:', url);
    console.log('Headers:', { ...headers, Authorization: `Bearer ${AIRTABLE_CONFIG.apiKey.substring(0, 10)}...` });
    console.log('Body:', JSON.stringify(payload, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(payload)
    });

    console.log('=== HTTP RESPONSE ===');
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response body:', errorText);
      throw new Error(`Errore Airtable (${response.status}): ${errorText}`);
    }

    const data = await response.json() as { records: Array<{ id: string; fields: Record<string, unknown> }> };
    console.log('Success response body:', JSON.stringify(data, null, 2));
    return data;
  } catch (error) {
    console.error('Errore durante la creazione della prenotazione:', error);
    throw error;
  }
}

// Funzione per caricare i servizi dal file JSON statico
function loadStaticServices() {
  try {
    const filePath = path.join(__dirname, 'servizi.json');
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(fileContent);

    console.log(`Loaded ${data.records.length} services from static JSON file`);

    // Trasforma i dati nel formato richiesto dal frontend
    const mappedServices = data.records.map((record: any) => {
      const name = record.fields.Servizi || record.fields.Codice;
      const price = record.fields.Prezzo || 0;
      const duration = Math.round((record.fields.Durata || 1800) / 60); // Converti da secondi a minuti

      // Determina la categoria basandosi sul nome del servizio
      let category = 'Altro';
      if (name) {
        const nameLower = name.toLowerCase();
        if (nameLower.includes('viso') || nameLower.includes('pulizia') || nameLower.includes('trattamento') || nameLower.includes('peeling')) {
          category = 'Viso';
        } else if (nameLower.includes('laser')) {
          category = 'Laser';
        } else if (nameLower.includes('cera')) {
          category = 'Ceretta';
        } else if (nameLower.includes('massaggio') || nameLower.includes('endospheres') || nameLower.includes('pressoterapia') || nameLower.includes('bendaggi')) {
          category = 'Corpo';
        } else if (nameLower.includes('manicure') || nameLower.includes('pedicure') || nameLower.includes('semipermanente') || nameLower.includes('ricostruzione')) {
          category = 'Unghie';
        } else if (nameLower.includes('sopracciglia') || nameLower.includes('baffetto') || nameLower.includes('henné') || nameLower.includes('laminazione')) {
          category = 'Estetica';
        }
      }

      return {
        id: record.id,
        name: name || 'Servizio senza nome',
        price: price,
        category: category,
        description: record.fields.Descrizione || `${name} - Durata: ${duration} minuti`,
        duration: duration
      };
    }).filter((service: any) => service.name && service.name !== 'Servizi' && service.name !== 'Codice' && service.price !== undefined);

    console.log(`Mapped ${mappedServices.length} valid services`);
    return mappedServices;
  } catch (error) {
    console.error('Errore durante il caricamento del file servizi.json:', error);
    return [];
  }
}

// Modifico la funzione getServices per usare il file statico
export async function getServices() {
  console.log('Loading services from static JSON file...');
  return loadStaticServices();
}

// Modifico la funzione getServiceCategories per usare categorie statiche
export async function getServiceCategories() {
  console.log('Loading service categories from static data...');

  // Estrae le categorie uniche dai servizi caricati
  const services = loadStaticServices();
  const categorySet = new Set(services.map((service: any) => service.category));
  const categories = Array.from(categorySet);

  return categories.map((category, index) => ({
    id: `cat_${index}`,
    name: category,
    displayOrder: index
  }));
}

// Ottieni gli slot orari disponibili - seguendo il pattern del codice Python
export async function getAvailableTimeSlots(date: string) {
  if (!isAirtableConfigured()) {
    return [
      "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
      "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00"
    ];
  }

  try {
    // Usa il formato del filtro dal codice Python funzionante
    const filterFormula = `AND(FIND("${date}", {Data Appuntamento}) > 0, NOT({STATO} = "CANCELLATO"))`;
    const params = new URLSearchParams({
      filterByFormula: filterFormula
    });

    const url = `https://api.airtable.com/v0/${AIRTABLE_CONFIG.baseId}/tblAanKl9ORXkyFEr`;

    const response = await fetch(`${url}?${params}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AIRTABLE_CONFIG.apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      console.error('Errore nel recupero prenotazioni esistenti, usando slot predefiniti');
      return [
        "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
        "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00"
      ];
    }

    const data = await response.json();
    const bookedSlots = (data as any).records.map((record: any) => {
      const datetime = record.fields['Data Appuntamento'];
      if (datetime && datetime.includes(' ')) {
        return datetime.split(' ')[1]; // Estrae l'ora
      }
      return null;
    }).filter((slot: any) => slot !== null);

    const allSlots = [
      "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
      "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00"
    ];

    const availableSlots = allSlots.filter(slot => !bookedSlots.includes(slot));

    console.log(`Slot disponibili per ${date}:`, availableSlots);
    return availableSlots;
  } catch (error) {
    console.error('Errore nel recupero degli slot orari:', error);
    return [
      "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
      "14:00", "14:30", "15:00", "15:30", "16:00", "16:30", "17:00"
    ];
  }
}

// Recupera le prenotazioni di un cliente tramite telefono o email
export async function getClientAppointments(clientIdentifier: string) {
  if (!isAirtableConfigured()) {
    throw new Error('Airtable API non configurata');
  }

  try {
    // Cerca prima per telefono, poi per email nel nome
    const filterFormula = `OR(FIND("${clientIdentifier}", {Telefono}) > 0, FIND("${clientIdentifier}", {NOME DA APP}) > 0)`;

    const response = await fetch(
      `${AIRTABLE_CONFIG.apiUrl}/${AIRTABLE_CONFIG.baseId}/${AIRTABLE_CONFIG.appointmentsTable}?filterByFormula=${encodeURIComponent(filterFormula)}&sort[0][field]=Data Appuntamento&sort[0][direction]=desc`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${AIRTABLE_CONFIG.apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Errore Airtable: ${(errorData as any).error?.message || 'Errore sconosciuto'}`);
    }

    const data = await response.json();
    const appointments = (data as any).records.map((record: any) => ({
      id: record.id,
      cliente: record.fields['NOME DA APP'] || 'Cliente sconosciuto',
      servizio: record.fields['TRATTAMENTO'] || 'Servizio non specificato',
      operatrice: record.fields['Operatrici'] || 'Non assegnata',
      dataAppuntamento: record.fields['Data Appuntamento'] || '',
      stato: record.fields['STATO'] || 'RICHIESTO',
      telefono: record.fields['Telefono'] || '',
      note: record.fields['NOTE'] || ''
    }));

    console.log(`Prenotazioni trovate per ${clientIdentifier}:`, appointments.length);
    return appointments;
  } catch (error) {
    console.error('Errore nel recupero delle prenotazioni del cliente:', error);
    throw error;
  }
}

// Test della connessione ad Airtable - seguendo il formato del codice Python
export async function testAirtableConnection() {
  console.log('=== AIRTABLE TEST CONNECTION ===');
  console.log('API Key present:', !!AIRTABLE_CONFIG.apiKey);
  console.log('API Key length:', AIRTABLE_CONFIG.apiKey?.length);
  console.log('Base ID:', AIRTABLE_CONFIG.baseId);

  if (!isAirtableConfigured()) {
    return { success: false, error: 'API key non configurata' };
  }

  try {
    const url = `${AIRTABLE_CONFIG.apiUrl}/${AIRTABLE_CONFIG.baseId}/tblAanKl9ORXkyFEr`;
    const params = new URLSearchParams({
      maxRecords: '1'
    });

    const fullUrl = `${url}?${params}`;
    const headers = {
      'Authorization': `Bearer ${AIRTABLE_CONFIG.apiKey}`,
      'Content-Type': 'application/json'
    };

    console.log('=== HTTP REQUEST ===');
    console.log('Method: GET');
    console.log('URL:', fullUrl);
    console.log('Headers:', { ...headers, Authorization: `Bearer ${AIRTABLE_CONFIG.apiKey.substring(0, 10)}...` });

    const response = await fetch(fullUrl, {
      method: 'GET',
      headers
    });

    console.log('=== HTTP RESPONSE ===');
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response body:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { error: { message: errorText } };
      }

      return {
        success: false,
        error: `Errore Airtable (${response.status}): ${errorData.error?.message || errorText}`,
        details: { status: response.status, response: errorText }
      };
    }

    const data = await response.json();
    console.log('Success response body:', JSON.stringify(data, null, 2));
    return {
      success: true,
      message: `Connessione riuscita. Base: ${AIRTABLE_CONFIG.baseId}, Records trovati: ${(data as any).records?.length || 0}`,
      data: data
    };
  } catch (error) {
    console.error('Connection error:', error);
    return {
      success: false,
      error: `Errore di connessione: ${error}`
    };
  }
}

// Trova o crea un cliente nella tabella Clienti
export async function findOrCreateClient(clientData: any) {
  console.log('=== AIRTABLE FIND OR CREATE CLIENT ===');
  console.log('Input data:', JSON.stringify(clientData, null, 2));

  if (!isAirtableConfigured()) {
    throw new Error('Airtable API non configurata');
  }

  try {
    // Prima cerca se il cliente esiste già
    const searchUrl = `${AIRTABLE_CONFIG.apiUrl}/${AIRTABLE_CONFIG.baseId}/${AIRTABLE_CONFIG.clientsTable}?filterByFormula=${encodeURIComponent(`{Email}="${clientData.email}"`)}`;
    const headers = {
      'Authorization': `Bearer ${AIRTABLE_CONFIG.apiKey}`,
      'Content-Type': 'application/json'
    };

    console.log('=== HTTP REQUEST (SEARCH) ===');
    console.log('Method: GET');
    console.log('URL:', searchUrl);
    console.log('Headers:', { ...headers, Authorization: `Bearer ${AIRTABLE_CONFIG.apiKey.substring(0, 10)}...` });

    const searchResponse = await fetch(searchUrl, {
      method: 'GET',
      headers
    });

    console.log('=== HTTP RESPONSE (SEARCH) ===');
    console.log('Status:', searchResponse.status);
    console.log('Status Text:', searchResponse.statusText);

    if (!searchResponse.ok) {
      const errorText = await searchResponse.text();
      console.error('Search error response body:', errorText);
      throw new Error(`Errore Airtable: ${errorText}`);
    }

    const searchData = await searchResponse.json();
    console.log('Search response body:', JSON.stringify(searchData, null, 2));

    // Se il cliente esiste, restituiscilo
    if ((searchData as any).records.length > 0) {
      console.log('Client found, returning existing client');
      return (searchData as any).records[0];
    }

    // Se non esiste, crealo
    const createPayload = {
      fields: {
        Nome: clientData.nome,
        Cognome: clientData.cognome,
        Email: clientData.email,
        Telefono: clientData.telefono,
        'Data Registrazione': new Date().toISOString()
      }
    };

    const createUrl = `${AIRTABLE_CONFIG.apiUrl}/${AIRTABLE_CONFIG.baseId}/${AIRTABLE_CONFIG.clientsTable}`;

    console.log('=== HTTP REQUEST (CREATE) ===');
    console.log('Method: POST');
    console.log('URL:', createUrl);
    console.log('Headers:', { ...headers, Authorization: `Bearer ${AIRTABLE_CONFIG.apiKey.substring(0, 10)}...` });
    console.log('Body:', JSON.stringify(createPayload, null, 2));

    const createResponse = await fetch(createUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify(createPayload)
    });

    console.log('=== HTTP RESPONSE (CREATE) ===');
    console.log('Status:', createResponse.status);
    console.log('Status Text:', createResponse.statusText);

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      console.error('Create error response body:', errorText);
      throw new Error(`Errore Airtable: ${errorText}`);
    }

    const createData = await createResponse.json();
    console.log('Create response body:', JSON.stringify(createData, null, 2));
    return createData;
  } catch (error) {
    console.error('Errore durante la gestione del cliente:', error);
    throw error;
  }
}

// Autenticazione cliente tramite email
export async function authenticateClient(email: string) {
  if (!isAirtableConfigured()) {
    throw new Error('Airtable API non configurata');
  }

  try {
    const response = await fetch(
      `${AIRTABLE_CONFIG.apiUrl}/${AIRTABLE_CONFIG.baseId}/${AIRTABLE_CONFIG.clientsTable}?filterByFormula=${encodeURIComponent(`{Email}="${email}"`)}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${AIRTABLE_CONFIG.apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Errore Airtable: ${(errorData as any).error?.message || 'Errore sconosciuto'}`);
    }

    const data = await response.json();

    if ((data as any).records.length > 0) {
      const client = (data as any).records[0];
      return {
        id: client.id,
        nome: client.fields.Nome,
        cognome: client.fields.Cognome,
        email: client.fields.Email,
        telefono: client.fields.Telefono,
        dataRegistrazione: client.fields['Data Registrazione']
      };
    }

    return null;
  } catch (error) {
    console.error('Errore durante l\'autenticazione del cliente:', error);
    throw error;
  }
}