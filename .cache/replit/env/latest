declare -gx REPL_OWNER=lorenzogranieri
declare -gx REPL_IMAGE=gcr.io/marine-cycle-160323/nix:bf8590a3e2f0a8b70b7ca175eeed9074dffbfca9
declare -gx REPLIT_DB_URL=https://kv.replit.com/v0/eyJhbGciOiJIUzUxMiIsImlzcyI6ImNvbm1hbiIsImtpZCI6InByb2Q6MSIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJjb25tYW4iLCJleHAiOjE3NDg2NDA1MjksImlhdCI6MTc0ODUyODkyOSwiZGF0YWJhc2VfaWQiOiI0NzI2MzIzNC1lZDQ2LTQ3YzYtYWYzNS00YWRjYjE4NDg3MzQifQ.MuupvpGrPDuTphR3IRftYbbkxRAcHi-ylFEyP10fbj5YOWC808ANaVJ1a-t-zTDt_E_tq9PHuW_FwCS4u7G-WA
declare -gx GIT_ASKPASS=replit-git-askpass
declare -gx REPLIT_BASHRC=/nix/store/7mbv5hcsh9cj7pk4maggp301fma07cm0-replit-bashrc/bashrc
declare -gx REPLIT_DOMAINS=47263234-ed46-47c6-af35-4adcb1848734-00-3rx1va6s468bd.picard.replit.dev
declare -gx DOCKER_CONFIG=/home/<USER>/workspace/.config/docker
declare -gx REPLIT_SUBCLUSTER=paid
declare -gx LOCALE_ARCHIVE=/usr/lib/locale/locale-archive
declare -gx REPLIT_PID1_FLAG_REPLIT_RTLD_LOADER=1
declare -gx REPLIT_LD_AUDIT=/nix/store/n5x1kgbz8zjh63ymsijbislyi1n1hir6-replit_rtld_loader-1/rtld_loader.so
declare -gx REPLIT_RIPPKGS_INDICES=/nix/store/l5gcmdp908sji4wchfp8csflhjcgnmm3-rippkgs-indices
declare -gx REPL_LANGUAGE=nix
declare -gx XDG_CACHE_HOME=/home/<USER>/workspace/.cache
declare -gx npm_config_prefix=/home/<USER>/workspace/.config/npm/node_global
declare -gx HOME=/home/<USER>
declare -gx REPLIT_DEV_DOMAIN=47263234-ed46-47c6-af35-4adcb1848734-00-3rx1va6s468bd.picard.replit.dev
declare -gx REPLIT_CLUSTER=picard
declare -gx REPL_IDENTITY_KEY=k2.secret.7nOu-ntovxxkeQcMaUg2fV-Esz2JmjwiGa5Ek04NEBWLZ-tdVnUVZ-ayXGmhz1o08KMpcF0nedduaF8zBH5zBg
declare -gx XDG_CONFIG_HOME=/home/<USER>/workspace/.config
declare -gx XDG_DATA_HOME=/home/<USER>/workspace/.local/share
declare -gx COLORTERM=truecolor
declare -gx USER=runner
declare -gx NIX_PROFILES='/nix/var/nix/profiles/default /home/<USER>/.nix-profile'
declare -gx LANG=en_US.UTF-8
declare -gx REPLIT_PID1_VERSION=0.0.0-b1d6c11
declare -gx REPL_IDENTITY=v2.public.Q2lRME56STJNekl6TkMxbFpEUTJMVFEzWXpZdFlXWXpOUzAwWVdSallqRTRORGczTXpRU0QyeHZjbVZ1ZW05bmNtRnVhV1Z5YVJvT1FtVmhkWFI1UW05dmEybHVaM01pSkRRM01qWXpNak0wTFdWa05EWXRORGRqTmkxaFpqTTFMVFJoWkdOaU1UZzBPRGN6TkRpQ21mSVNXZzRLQm5CcFkyRnlaQklFY0dGcFpBPT06vy9hECOHOXUY5WTwXYg9OjMsXeUxn4fOPL0pF71914gcruAsfb5QT-ZxsorVAyZAzj2gqNMqv2q0ynEYjhsP.R0FFaUJtTnZibTFoYmhLUkNIWXlMbkIxWW14cFl5NVJNbVF6VTFkME1FNHlhRE5WVm14U1dqSktjRlZIZEROVFZrNUZVVmR3YjAxSVZuVlJhMHB2VWtSR01VNURkRlZSVjJoMlVUQmtRbFpYUmt0YU1qbHlWR3RTYW1WVk5YRlVXR3hPWld4R01GZHNaRkpOUlRWd1RVUkNUMDFyTUhsVVJtUkhZbFV4TmxaWVVrOVNNRnB5VjFSS1NtVkZPVVZWVkZKUFpXc3dkMUl5YUVaVk1GRjVaVWhhYW1KV1dqRmFWekExWW0xT2RGSnVWbWhXTVZvMVdWWktkbEpyT1VwVE1XODBZVVZzYUZFd1RrcFNNazVJWWtkd1dsZEZjSEpTTW1SYVkxVktTVkZ0YUdoV01VWndWR3hrZW1WVmVIVlJha1phWWxob2QxZFlhekZqUlRGMFYyNXNXRkpzY0doVVZsWmhWMFp3ZEUxWWNHbFRSMmd6V1dwS1RrNVdiRlpPVmtaU1RXNUNUVll3Vmt0aE1VNXhWRzVXV0ZJd2NEQlpWV1JoVkcxUmVGTllVbHBOTWxKaFpEQTVPRE5mT1ROQ1kwVjZObmhpYmxsbFpYVkJkbXRwU1hFNVFWcHpkMFI1TkhGWE1rY3liMDVYVkVoSWRIcDROVVkyUmxGTmJqUXlVMmd5YWtVNVpVZElaRU5yV0ZsRWRESkpTVzVrZVROdVJ5MHRRMmN1VWpCR1JtRlZTblJVYmxwcFlsUkdiMWx0YUUxWmEwWjFWMWhzVFdKclNYaFhWekUwWTBac05VNVdTazVpVmtreVZsUkdhMDFIVm5SVGJGcFVZbTVDVjFadE1UUlZNVkp5VlcxR1RsWnVRbGRWTW5SUFZrWmFXV0ZGVmxabGEwcHlWV3BCTVZOV1JuSlRiRnBPVW14d1UxWnRjRTlaVjFKWFlqTm9VMkpYYUZOV2FrcHZaRlpXV0dSSGRHbGlSVFZZV1d0V1QxWnRTbFZpUlZaV1lXdEtTRnBIZUhOV2JFcDFVbXhLVjFaWVFrcFdNbkJEWXpGa2MxSnNhR2hUUm5CVFZGVmtVMUV4V2tkYVJXUlNZbFZhU1ZkclZYaFZNREYwVld0MFYwMVdXbFJWVkVwS1pERlNjbUZHU2xkaE1YQjJWbFphYTJJeVNuTlVia3BwVTBWYVdGbHRkSGRVTVd4WFZXeGtUazFZUWtoWGExWXdZV3N4Y2xkc2JGZFNiV2hZVmtSR1lXUkhWa2xqUm1SWFlsWktTVlpHVWt0VU1rMTVVMnBhVm1GNmJGaFVWM2hMWWpGWmVVMVVVbFJOYTFwSFZGWldhMVpIU2taWGJGcGFWbnBGTUZkV1duTk9iRVpWVW0xd2FWSllRalpXUkVaWFdWZEZlVk5zYkZaV1JWcFhXV3RhWVdOc2NFaGxSVnBzVW01Q1JsWXlNWGRoUjBWNFkwYzVWMkZyV2xSVmVrWk9aVVphYzFOc1JsZFNSVW96VmpKMFlWZHRUblJqUlRGUVYwVTBlbHBGVmxwT1ZuQkZVbGhTYVdKVVZsRlVNR1JoVlcxS1dHRkVTbFJTVm5CNFZtdFdjbVJIVWtWaFJYQnBZbFp3VVZkRVNYaFdWVEYwV1hwU2FsZElRa1pWYTJSV1RrWmFSV0pHVW1oTlZrbzJWMjE0YjJKWFZuSmlla0pZVmxVeE5sZHRjM2RsYkdSV1RsaEtWRlpIVWxsWFYydDNUbFpLY2xWdE9VOWhWRVpNVkdwSk5WSkZlSE5UV0dSVFlURndiMVpzVm5kTlJscElUbGRHYUZZd2NGWlZiVEExVjIxS1dGVnFTbFpoYTNCUVZURmFUMlJXWkhSU2JFNVRaVzFuTUE9PQ
declare -gx REPL_SLUG=workspace
declare -gx REPL_HOME=/home/<USER>/workspace
read -r _new_path <<< "/nix/store/0z5iwcvalafm3j2c5pfhllsfbxrbyzf4-postgresql-16.5/bin:/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/sww4j97gzg3wl9rykrmjfy8736iiz11n-pid1/bin:/nix/store/iz3j6pp7zlvk5kzwfiawz6g4lph67vji-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
#PATH=/nix/store/0z5iwcvalafm3j2c5pfhllsfbxrbyzf4-postgresql-16.5/bin:/nix/store/r2wrscmjzn4f2f7wk7q2ms2h96mjwzv7-npx/bin:/home/<USER>/workspace/.config/npm/node_global/bin:/home/<USER>/workspace/node_modules/.bin:/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin:/nix/store/5q4dz85wgqhifng1fk2xy85pslkmiqvs-bun-1.2.12/bin:/nix/store/z8s3r4vwf4r26g2d7shnw5lva6ihim8f-pnpm-9.15.0/bin:/nix/store/jcgdksj946l5l42c2y9ks2l4g6n74h3f-yarn-1.22.22/bin:/nix/store/2s17mrby0ph00z22rkabfs9vzpzx1r70-prettier-3.3.3/bin:/nix/store/sww4j97gzg3wl9rykrmjfy8736iiz11n-pid1/bin:/nix/store/iz3j6pp7zlvk5kzwfiawz6g4lph67vji-replit-runtime-path/bin:/home/<USER>/.nix-profile/bin:/home/<USER>/.local/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
if [ -e "/run/replit/env/last" ]; then read -r _last_path < <(\grep '^#PATH=' /run/replit/env/last | cut -f 2 -d =); fi
_user_components="$(\tr : $'\n' <<< "${PATH:-}" |\grep -xv -f <(\tr : $'\n' <<< "${_last_path}") |\tr $'\n' :)"
declare -gx PATH="${_user_components}${_new_path}"
declare -gx NIXPKGS_ALLOW_UNFREE=1
declare -gx __EGL_VENDOR_LIBRARY_FILENAMES=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/share/glvnd/egl_vendor.d/50_mesa.json
declare -gx REPL_OWNER_ID=39619714
declare -gx REPLIT_ENVIRONMENT=production
declare -gx GIT_EDITOR=replit-git-editor
declare -gx REPLIT_RTLD_LOADER=1
declare -gx PROMPT_DIRTRIM=2
declare -gx NIX_PS1='\[\033[01;34m\]\w\[\033[00m\]\$ '
declare -gx REPLIT_CLI=/nix/store/kg7y2cbq8jfcs6qj2hikk83q594qnzpc-pid1-0.0.1/bin/replit
declare -gx NIX_PATH=nixpkgs=/home/<USER>/.nix-defexpr/channels/nixpkgs-stable-24_05:/home/<USER>/.nix-defexpr/channels
declare -gx DISPLAY=:0
declare -gx REPLIT_NIX_CHANNEL=stable-24_05
declare -gx REPL_PUBKEYS='{"crosis-ci":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:1":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","crosis-ci:latest":"7YlpcYh82oR9NSTtSYtR5jDL4onNzCGJGq6b+9CuZII=","prod":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:1":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","prod:2":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:3":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:4":"8uGN+vfszlnV93/HCSHlVLG0xddMlPkir1Ni4JKT4+w=","prod:5":"9+MCOSHQSQlcodXoot8dC8NLhc862nLkx1/VMsbY2h8=","prod:latest":"tGsjlu/BJvWTgvMaX7acuUb7AO1dXOrRiuk7y083RFE=","vault-goval-token":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:1":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E=","vault-goval-token:latest":"D5jJoMx1Ml54HM92NLgXl+MzptwDqbSsfyFG6f52g9E="}'
declare -gx LIBGL_DRIVERS_PATH=/nix/store/1z62rda9iqnxi4ryvgmyvfaj979hgk7s-mesa-24.2.8-drivers/lib/dri
declare -gx REPLIT_PID1_FLAG_NIXMODULES_BEFORE_REPLIT_NIX=1
declare -gx REPL_ID=47263234-ed46-47c6-af35-4adcb1848734
declare -gx HOSTNAME=d5398feb61f5
declare -gx XDG_DATA_DIRS=/nix/store/iz3j6pp7zlvk5kzwfiawz6g4lph67vji-replit-runtime-path/share
