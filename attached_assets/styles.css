/* 
 * Aphrodite PWA - Stili principali
 * Palette colori e tipografia coerenti con il template servizi
 */

:root {
  /* Palette colori principale */
  --color-gold: #d4af37;
  --color-gold-light: #e9d18b;
  --color-gold-dark: #b8860b;
  --color-white: #ffffff;
  --color-off-white: #f9f9f9;
  --color-light-gray: #f0f0f0;
  --color-medium-gray: #cccccc;
  --color-dark-gray: #333333;
  --color-black: #000000;
  
  /* Colori funzionali */
  --color-primary: var(--color-gold);
  --color-primary-light: var(--color-gold-light);
  --color-primary-dark: var(--color-gold-dark);
  --color-secondary: var(--color-dark-gray);
  --color-background: var(--color-white);
  --color-text: var(--color-dark-gray);
  --color-text-light: var(--color-white);
  --color-border: var(--color-medium-gray);
  --color-success: #4caf50;
  --color-error: #f44336;
  --color-warning: #ff9800;
  --color-info: #2196f3;
  
  /* Tipografia */
  --font-primary: 'Montserrat', sans-serif;
  --font-secondary: 'Playfair Display', serif;
  --font-accent: 'Great Vibes', cursive;
  --font-body: 'Open Sans', sans-serif;
  
  /* Dimensioni font */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-md: 1.125rem;  /* 18px */
  --font-size-lg: 1.25rem;   /* 20px */
  --font-size-xl: 1.5rem;    /* 24px */
  --font-size-2xl: 1.875rem; /* 30px */
  --font-size-3xl: 2.25rem;  /* 36px */
  --font-size-4xl: 3rem;     /* 48px */
  
  /* Spaziature */
  --spacing-xs: 0.25rem;  /* 4px */
  --spacing-sm: 0.5rem;   /* 8px */
  --spacing-md: 1rem;     /* 16px */
  --spacing-lg: 1.5rem;   /* 24px */
  --spacing-xl: 2rem;     /* 32px */
  --spacing-2xl: 3rem;    /* 48px */
  
  /* Border radius */
  --border-radius-sm: 0.25rem;  /* 4px */
  --border-radius-md: 0.5rem;   /* 8px */
  --border-radius-lg: 1rem;     /* 16px */
  --border-radius-full: 9999px;
  
  /* Box shadow */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Transizioni */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Reset e base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-body);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--color-text);
  background-color: var(--color-background);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-secondary);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--color-secondary);
}

h1 {
  font-size: var(--font-size-3xl);
}

h2 {
  font-size: var(--font-size-2xl);
}

h3 {
  font-size: var(--font-size-xl);
}

h4 {
  font-size: var(--font-size-lg);
}

h5 {
  font-size: var(--font-size-md);
}

h6 {
  font-size: var(--font-size-base);
}

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-primary-dark);
}

img {
  max-width: 100%;
  height: auto;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -var(--spacing-md);
}

.col {
  flex: 1;
  padding: 0 var(--spacing-md);
}

.col-2 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 var(--spacing-md);
}

.col-3 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 0 var(--spacing-md);
}

.col-4 {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 0 var(--spacing-md);
}

/* Utilità */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.mb-0 {
  margin-bottom: 0;
}

.mb-1 {
  margin-bottom: var(--spacing-xs);
}

.mb-2 {
  margin-bottom: var(--spacing-sm);
}

.mb-3 {
  margin-bottom: var(--spacing-md);
}

.mb-4 {
  margin-bottom: var(--spacing-lg);
}

.mb-5 {
  margin-bottom: var(--spacing-xl);
}

.mt-0 {
  margin-top: 0;
}

.mt-1 {
  margin-top: var(--spacing-xs);
}

.mt-2 {
  margin-top: var(--spacing-sm);
}

.mt-3 {
  margin-top: var(--spacing-md);
}

.mt-4 {
  margin-top: var(--spacing-lg);
}

.mt-5 {
  margin-top: var(--spacing-xl);
}

.w-100 {
  width: 100%;
}

.d-flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

/* Componenti */
.btn {
  display: inline-block;
  font-family: var(--font-primary);
  font-weight: 600;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.5rem 1.5rem;
  font-size: var(--font-size-base);
  line-height: 1.5;
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.btn-primary {
  color: var(--color-white);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-primary:hover {
  color: var(--color-white);
  background-color: var(--color-primary-dark);
  border-color: var(--color-primary-dark);
}

.btn-secondary {
  color: var(--color-primary);
  background-color: transparent;
  border-color: var(--color-primary);
}

.btn-secondary:hover {
  color: var(--color-white);
  background-color: var(--color-primary);
  border-color: var(--color-primary);
}

.btn-large {
  padding: 0.75rem 2rem;
  font-size: var(--font-size-lg);
}

.btn-small {
  padding: 0.25rem 1rem;
  font-size: var(--font-size-sm);
}

.card {
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-content {
  padding: var(--spacing-lg);
}

.card-title {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--color-primary);
}

.card-price {
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
}

/* Form */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 600;
  color: var(--color-secondary);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-white);
  background-clip: padding-box;
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-sm);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
  border-color: var(--color-primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
}

select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='4' viewBox='0 0 8 4'%3E%3Cpath fill='%23333' d='M0 0l4 4 4-4z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 8px 4px;
  padding-right: 2.5rem;
}

textarea.form-control {
  height: auto;
  min-height: 100px;
  resize: vertical;
}

.form-check {
  display: flex;
  align-items: center;
}

.form-check input[type="checkbox"] {
  margin-right: var(--spacing-xs);
}

/* Servizi */
.service-list-container {
  padding: var(--spacing-lg);
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-xl);
}

.service-category-title {
  color: var(--color-primary);
  font-size: var(--font-size-xl);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--color-primary-light);
}

.service-list {
  margin-bottom: var(--spacing-xl);
}

.service-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px dashed var(--color-light-gray);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.service-item:hover {
  background-color: var(--color-light-gray);
}

.service-item.selected {
  background-color: var(--color-primary-light);
}

.service-name {
  font-weight: 500;
  color: var(--color-secondary);
}

.service-price {
  font-weight: 700;
  color: var(--color-primary);
  font-size: var(--font-size-md);
}

/* Header e Footer */
.site-header {
  background-color: var(--color-white);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  height: 50px;
  width: auto;
}

.site-footer {
  background-color: var(--color-secondary);
  color: var(--color-white);
  padding: var(--spacing-xl) 0;
  margin-top: var(--spacing-2xl);
}

.footer-logo {
  height: 40px;
  width: auto;
  margin-bottom: var(--spacing-md);
}

.footer-title {
  color: var(--color-primary);
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-md);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--spacing-xs);
}

.footer-links a {
  color: var(--color-white);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--color-primary-light);
}

.social-links {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: var(--color-primary);
  color: var(--color-white);
  border-radius: var(--border-radius-full);
  transition: background-color var(--transition-fast);
}

.social-link:hover {
  background-color: var(--color-primary-dark);
  color: var(--color-white);
}

.footer-bottom {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

/* Sezioni */
.section {
  padding: var(--spacing-2xl) 0;
}

.section-bg-light {
  background-color: var(--color-light-gray);
}

.section-bg-gold {
  background-color: var(--color-primary-light);
  color: var(--color-secondary);
}

.section-title {
  text-align: center;
  margin-bottom: var(--spacing-sm);
  color: var(--color-secondary);
}

.section-subtitle {
  text-align: center;
  font-family: var(--font-accent);
  font-size: var(--font-size-xl);
  color: var(--color-primary);
  margin-bottom: var(--spacing-xl);
}

/* Animazioni */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity var(--transition-normal), transform var(--transition-normal);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-up {
  opacity: 0;
  transform: translateY(40px);
  transition: opacity var(--transition-slow), transform var(--transition-slow);
}

.slide-up.visible {
  opacity: 1;
  transform: translateY(0);
}

/* PWA specifici */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-content {
  flex: 1;
}

.app-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: var(--color-white);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.app-nav-list {
  display: flex;
  justify-content: space-around;
  list-style: none;
  padding: 0;
  margin: 0;
}

.app-nav-item {
  flex: 1;
  text-align: center;
}

.app-nav-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  color: var(--color-secondary);
  transition: color var(--transition-fast);
}

.app-nav-link.active {
  color: var(--color-primary);
}

.app-nav-icon {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-xs);
}

.app-nav-text {
  font-size: var(--font-size-xs);
}

.offline-indicator {
  background-color: var(--color-warning);
  color: var(--color-white);
  text-align: center;
  padding: var(--spacing-xs);
  font-size: var(--font-size-sm);
}

.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(212, 175, 55, 0.3);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 992px) {
  .col-3, .col-4 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .col-2, .col-3, .col-4 {
    flex: 0 0 100%;
    max-width: 100%;
  }
  
  .service-item {
    flex-direction: column;
    align-items: flex-start;
    padding: var(--spacing-md) 0;
  }
  
  .service-price {
    margin-top: var(--spacing-xs);
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  .section {
    padding: var(--spacing-xl) 0;
  }
  
  h1 {
    font-size: var(--font-size-2xl);
  }
  
  h2 {
    font-size: var(--font-size-xl);
  }
}

/* Datepicker personalizzato */
.datepicker-container {
  position: relative;
}

.datepicker {
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-md);
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  width: 300px;
  margin-top: var(--spacing-xs);
}

.datepicker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.datepicker-month-year {
  font-weight: 600;
  color: var(--color-primary);
}

.datepicker-nav-btn {
  background: none;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  font-size: var(--font-size-lg);
  padding: var(--spacing-xs);
}

.datepicker-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.datepicker-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: var(--spacing-xs);
}

.datepicker-day {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 36px;
  border-radius: var(--border-radius-full);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.datepicker-day:hover {
  background-color: var(--color-light-gray);
}

.datepicker-day.selected {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.datepicker-day.disabled {
  color: var(--color-medium-gray);
  cursor: not-allowed;
}

/* Timepicker personalizzato */
.timepicker-container {
  position: relative;
}

.timepicker {
  background-color: var(--color-white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-md);
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 10;
  width: 200px;
  margin-top: var(--spacing-xs);
}

.timepicker-slots {
  max-height: 200px;
  overflow-y: auto;
}

.timepicker-slot {
  padding: var(--spacing-sm);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  border-radius: var(--border-radius-sm);
}

.timepicker-slot:hover {
  background-color: var(--color-light-gray);
}

.timepicker-slot.selected {
  background-color: var(--color-primary);
  color: var(--color-white);
}

.timepicker-slot.disabled {
  color: var(--color-medium-gray);
  cursor: not-allowed;
}

/* Toast notifications */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.toast {
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  min-width: 250px;
  max-width: 350px;
  animation: slideIn var(--transition-normal);
}

.toast-success {
  background-color: var(--color-success);
  color: white;
}

.toast-error {
  background-color: var(--color-error);
  color: white;
}

.toast-warning {
  background-color: var(--color-warning);
  color: white;
}

.toast-info {
  background-color: var(--color-info);
  color: white;
}

.toast-icon {
  margin-right: var(--spacing-sm);
  font-size: var(--font-size-lg);
}

.toast-message {
  flex: 1;
}

.toast-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: var(--font-size-lg);
  opacity: 0.7;
  transition: opacity var(--transition-fast);
}

.toast-close:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Installazione PWA prompt */
.install-prompt {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-white);
  padding: var(--spacing-md);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
  transform: translateY(100%);
  transition: transform var(--transition-normal);
}

.install-prompt.show {
  transform: translateY(0);
}

.install-prompt-text {
  flex: 1;
  margin-right: var(--spacing-md);
}

.install-prompt-actions {
  display: flex;
  gap: var(--spacing-sm);
}
