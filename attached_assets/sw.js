// sw.js - Service Worker per la PWA Aphrodite Center Store

const CACHE_NAME = 'aphrodite-pwa-v1';
const ASSETS_TO_CACHE = [
  '/',
  '/index.html',
  '/styles.css',
  '/app.js',
  '/airtable-api.js',
  '/email-service.js',
  '/sw-register.js',
  '/manifest.json',
  '/images/logo.png',
  '/images/icons/icon-72x72.png',
  '/images/icons/icon-96x96.png',
  '/images/icons/icon-128x128.png',
  '/images/icons/icon-144x144.png',
  '/images/icons/icon-152x152.png',
  '/images/icons/icon-192x192.png',
  '/images/icons/icon-384x384.png',
  '/images/icons/icon-512x512.png',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
  'https://fonts.googleapis.com/css2?family=Great+Vibes&family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;600&family=Playfair+Display:wght@400;700&display=swap'
];

// Installazione del Service Worker
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installazione in corso...');
  
  // Precaching delle risorse statiche
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] Caching delle risorse statiche');
        return cache.addAll(ASSETS_TO_CACHE);
      })
      .then(() => {
        console.log('[Service Worker] Installazione completata');
        return self.skipWaiting();
      })
  );
});

// Attivazione del Service Worker
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Attivazione in corso...');
  
  // Pulizia delle cache vecchie
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('[Service Worker] Eliminazione cache vecchia:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      console.log('[Service Worker] Attivazione completata');
      return self.clients.claim();
    })
  );
});

// Gestione delle richieste di rete
self.addEventListener('fetch', (event) => {
  // Ignora le richieste API
  if (event.request.url.includes('api.airtable.com') || 
      event.request.url.includes('api.emailjs.com')) {
    return;
  }
  
  // Strategia Cache First con Network Fallback
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Restituisci la risorsa dalla cache se disponibile
        if (response) {
          console.log('[Service Worker] Risorsa servita dalla cache:', event.request.url);
          return response;
        }
        
        // Altrimenti, effettua la richiesta di rete
        console.log('[Service Worker] Richiesta di rete per:', event.request.url);
        return fetch(event.request)
          .then((networkResponse) => {
            // Salva la risposta nella cache
            if (networkResponse && networkResponse.status === 200) {
              const responseToCache = networkResponse.clone();
              caches.open(CACHE_NAME)
                .then((cache) => {
                  cache.put(event.request, responseToCache);
                  console.log('[Service Worker] Risorsa salvata in cache:', event.request.url);
                });
            }
            
            return networkResponse;
          })
          .catch((error) => {
            console.error('[Service Worker] Errore di rete:', error);
            
            // Per le richieste di pagine HTML, restituisci la pagina offline
            if (event.request.headers.get('accept').includes('text/html')) {
              return caches.match('/offline.html');
            }
            
            return new Response('Errore di rete', {
              status: 503,
              statusText: 'Service Unavailable',
              headers: new Headers({
                'Content-Type': 'text/plain'
              })
            });
          });
      })
  );
});

// Gestione dei messaggi
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});

// Gestione delle sincronizzazioni in background
self.addEventListener('sync', (event) => {
  console.log('[Service Worker] Evento di sincronizzazione:', event.tag);
  
  if (event.tag === 'sync-bookings') {
    event.waitUntil(syncPendingBookings());
  }
});

// Funzione per sincronizzare le prenotazioni in attesa
async function syncPendingBookings() {
  try {
    // Ottieni le prenotazioni in attesa dall'IndexedDB
    const db = await openDatabase();
    const pendingBookings = await getPendingBookings(db);
    
    if (pendingBookings.length === 0) {
      console.log('[Service Worker] Nessuna prenotazione in attesa di sincronizzazione');
      return;
    }
    
    console.log(`[Service Worker] Sincronizzazione di ${pendingBookings.length} prenotazioni in corso...`);
    
    // Invia ogni prenotazione al server
    const results = await Promise.allSettled(
      pendingBookings.map(async (booking) => {
        try {
          // Simula l'invio della prenotazione
          // In una versione reale, qui effettueremmo la chiamata API
          console.log('[Service Worker] Invio prenotazione:', booking.id);
          
          // Rimuovi la prenotazione sincronizzata
          await removeBooking(db, booking.id);
          
          return { success: true, id: booking.id };
        } catch (error) {
          console.error('[Service Worker] Errore durante la sincronizzazione:', error);
          return { success: false, id: booking.id, error };
        }
      })
    );
    
    // Conta i successi e i fallimenti
    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.length - successful;
    
    console.log(`[Service Worker] Sincronizzazione completata: ${successful} successi, ${failed} fallimenti`);
    
    // Notifica il client
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'SYNC_COMPLETED',
        successful,
        failed
      });
    });
  } catch (error) {
    console.error('[Service Worker] Errore durante la sincronizzazione:', error);
  }
}

// Funzioni per la gestione dell'IndexedDB
function openDatabase() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('aphrodite-db', 1);
    
    request.onerror = (event) => {
      reject('Errore durante l\'apertura del database');
    };
    
    request.onsuccess = (event) => {
      resolve(event.target.result);
    };
    
    request.onupgradeneeded = (event) => {
      const db = event.target.result;
      
      // Crea l'object store per le prenotazioni
      if (!db.objectStoreNames.contains('pendingBookings')) {
        const store = db.createObjectStore('pendingBookings', { keyPath: 'id' });
        store.createIndex('timestamp', 'timestamp', { unique: false });
      }
    };
  });
}

function getPendingBookings(db) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['pendingBookings'], 'readonly');
    const store = transaction.objectStore('pendingBookings');
    const request = store.getAll();
    
    request.onerror = (event) => {
      reject('Errore durante il recupero delle prenotazioni');
    };
    
    request.onsuccess = (event) => {
      resolve(event.target.result);
    };
  });
}

function removeBooking(db, id) {
  return new Promise((resolve, reject) => {
    const transaction = db.transaction(['pendingBookings'], 'readwrite');
    const store = transaction.objectStore('pendingBookings');
    const request = store.delete(id);
    
    request.onerror = (event) => {
      reject('Errore durante la rimozione della prenotazione');
    };
    
    request.onsuccess = (event) => {
      resolve();
    };
  });
}
