// email-service.js - Servizio per l'invio di email di conferma

/**
 * Classe per la gestione dell'invio di email
 */
class EmailService {
  constructor() {
    this.emailApiUrl = 'https://api.emailjs.com/api/v1.0/email/send';
    this.serviceId = ''; // Da impostare in fase di produzione
    this.templateId = ''; // Da impostare in fase di produzione
    this.userId = ''; // Da impostare in fase di produzione
    this.isConfigured = false;
  }

  /**
   * Configura il servizio email
   * @param {Object} config - Configurazione del servizio
   */
  configure(config) {
    this.serviceId = config.serviceId;
    this.templateId = config.templateId;
    this.userId = config.userId;
    this.isConfigured = true;
    console.log('Email Service configurato con successo');
  }

  /**
   * Verifica se il servizio è configurato
   * @returns {boolean} - True se il servizio è configurato, false altrimenti
   */
  checkConfiguration() {
    if (!this.isConfigured) {
      console.warn('Email Service non configurato. Utilizzare il metodo configure() prima di inviare email.');
      return false;
    }
    return true;
  }

  /**
   * Invia un'email di conferma prenotazione
   * @param {Object} bookingData - Dati della prenotazione
   * @returns {Promise} - Promise con il risultato dell'invio
   */
  async sendConfirmationEmail(bookingData) {
    if (!this.checkConfiguration()) {
      return Promise.reject(new Error('Email Service non configurato'));
    }

    try {
      // Prepara i dati per il template
      const templateParams = {
        to_name: `${bookingData.nome} ${bookingData.cognome}`,
        to_email: bookingData.email,
        booking_date: bookingData.dataAppuntamento,
        booking_time: bookingData.oraAppuntamento,
        services: bookingData.servizi.join(', '),
        total_price: `€${bookingData.prezzoTotale.toFixed(2)}`,
        operator: bookingData.operatrice
      };

      // Effettua la chiamata API
      const response = await fetch(this.emailApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          service_id: this.serviceId,
          template_id: this.templateId,
          user_id: this.userId,
          template_params: templateParams
        })
      });

      // Verifica la risposta
      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Errore invio email: ${errorData || 'Errore sconosciuto'}`);
      }

      console.log('Email di conferma inviata con successo');
      return true;
    } catch (error) {
      console.error('Errore durante l\'invio dell\'email:', error);
      throw error;
    }
  }

  /**
   * Invia un'email di notifica al centro estetico
   * @param {Object} bookingData - Dati della prenotazione
   * @returns {Promise} - Promise con il risultato dell'invio
   */
  async sendNotificationEmail(bookingData) {
    if (!this.checkConfiguration()) {
      return Promise.reject(new Error('Email Service non configurato'));
    }

    try {
      // Prepara i dati per il template
      const templateParams = {
        client_name: `${bookingData.nome} ${bookingData.cognome}`,
        client_email: bookingData.email,
        client_phone: bookingData.telefono,
        booking_date: bookingData.dataAppuntamento,
        booking_time: bookingData.oraAppuntamento,
        services: bookingData.servizi.join(', '),
        total_price: `€${bookingData.prezzoTotale.toFixed(2)}`,
        operator: bookingData.operatrice,
        notes: bookingData.note || 'Nessuna nota'
      };

      // Effettua la chiamata API
      const response = await fetch(this.emailApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          service_id: this.serviceId,
          template_id: this.templateId + '_notification', // Template diverso per la notifica
          user_id: this.userId,
          template_params: templateParams
        })
      });

      // Verifica la risposta
      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Errore invio email di notifica: ${errorData || 'Errore sconosciuto'}`);
      }

      console.log('Email di notifica inviata con successo');
      return true;
    } catch (error) {
      console.error('Errore durante l\'invio dell\'email di notifica:', error);
      throw error;
    }
  }
}

// Esporta l'istanza del servizio
const emailService = new EmailService();
export default emailService;
