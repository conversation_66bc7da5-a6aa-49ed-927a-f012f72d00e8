// app.js - Logica principale della PWA Aphrodite Center Store

// Configurazione globale
const CONFIG = {
  apiBaseUrl: 'https://api.airtable.com/v0/',
  airtableBaseId: 'appbZ4ONNnQp69VtM',
  airtableTableName: 'DASHBOARD',
  apiKey: '', // Sarà impostata in fase di produzione
  operatrici: ['<PERSON>', '<PERSON><PERSON>', 'Viola Bianchi', '<PERSON>'],
  orariApertura: {
    1: { start: '09:00', end: '19:00' }, // Lunedì
    2: { start: '09:00', end: '19:00' }, // Martedì
    3: { start: '09:00', end: '19:00' }, // Mercoledì
    4: { start: '09:00', end: '19:00' }, // Giovedì
    5: { start: '09:00', end: '19:00' }, // Venerdì
    6: { start: '09:00', end: '13:00' }, // Sabato
    0: null // Domenica chiuso
  },
  slotDuration: 30, // Durata slot in minuti
  maxFutureDays: 30, // Massimo giorni prenotabili in futuro
};

// Stato dell'applicazione
const appState = {
  selectedServices: [],
  totalPrice: 0,
  isOnline: navigator.onLine,
  pendingBookings: [], // Prenotazioni in attesa di sincronizzazione
  currentView: 'serviziView',
  deferredPrompt: null, // Per l'installazione PWA
  currentUser: null,
  selectedDate: null,
  selectedTime: null,
};

// Cache per i dati
const dataCache = {
  services: [], // Servizi disponibili
  bookings: [], // Prenotazioni esistenti (per verificare disponibilità)
};

// Elementi DOM
const elements = {
  views: {
    serviziView: document.getElementById('serviziView'),
    prenotazioneView: document.getElementById('prenotazioneView'),
    confermaView: document.getElementById('confermaView'),
  },
  navigation: {
    navServizi: document.getElementById('navServizi'),
    navPrenota: document.getElementById('navPrenota'),
    navProfilo: document.getElementById('navProfilo'),
    navInfo: document.getElementById('navInfo'),
  },
  services: {
    serviceItems: document.querySelectorAll('.service-item'),
    selectedServicesContainer: document.getElementById('selectedServicesContainer'),
    selectedServicesList: document.getElementById('selectedServicesList'),
    totalPrice: document.getElementById('totalPrice'),
    proceedToBookingBtn: document.getElementById('proceedToBookingBtn'),
  },
  booking: {
    bookingServicesList: document.getElementById('bookingServicesList'),
    bookingTotalPrice: document.getElementById('bookingTotalPrice'),
    bookingForm: document.getElementById('bookingForm'),
    backToServicesBtn: document.getElementById('backToServicesBtn'),
    dataAppuntamento: document.getElementById('dataAppuntamento'),
    oraAppuntamento: document.getElementById('oraAppuntamento'),
    datepicker: document.getElementById('datepicker'),
    timepicker: document.getElementById('timepicker'),
  },
  confirmation: {
    confirmationEmail: document.getElementById('confirmationEmail'),
    confirmationDate: document.getElementById('confirmationDate'),
    confirmationTime: document.getElementById('confirmationTime'),
    confirmationOperator: document.getElementById('confirmationOperator'),
    newBookingBtn: document.getElementById('newBookingBtn'),
  },
  offline: {
    offlineIndicator: document.getElementById('offlineIndicator'),
  },
  install: {
    installPrompt: document.getElementById('installPrompt'),
    installBtn: document.getElementById('installBtn'),
    installLaterBtn: document.getElementById('installLaterBtn'),
  },
  toastContainer: document.getElementById('toastContainer'),
};

// Inizializzazione dell'app
document.addEventListener('DOMContentLoaded', () => {
  initApp();
});

// Funzione principale di inizializzazione
function initApp() {
  // Inizializza i listener per lo stato online/offline
  initNetworkListeners();
  
  // Inizializza i listener per l'installazione PWA
  initInstallPrompt();
  
  // Inizializza i listener per la navigazione
  initNavigationListeners();
  
  // Inizializza i listener per la selezione dei servizi
  initServiceSelectionListeners();
  
  // Inizializza i listener per il form di prenotazione
  initBookingFormListeners();
  
  // Inizializza i datepicker e timepicker
  initDateTimePickers();
  
  // Carica i dati dalla cache locale se disponibili
  loadCachedData();
  
  // Mostra la vista iniziale
  showView('serviziView');
  
  // Verifica se ci sono prenotazioni in attesa di sincronizzazione
  checkPendingBookings();
  
  // Mostra un messaggio di benvenuto
  showToast('Benvenuto in Aphrodite Center Store', 'info');
}

// Gestione dello stato online/offline
function initNetworkListeners() {
  // Aggiorna lo stato online/offline
  window.addEventListener('online', () => {
    appState.isOnline = true;
    elements.offline.offlineIndicator.style.display = 'none';
    showToast('Sei tornato online', 'success');
    
    // Tenta di sincronizzare le prenotazioni in attesa
    syncPendingBookings();
  });
  
  window.addEventListener('offline', () => {
    appState.isOnline = false;
    elements.offline.offlineIndicator.style.display = 'block';
    showToast('Sei offline. Le prenotazioni saranno salvate localmente.', 'warning');
  });
  
  // Imposta lo stato iniziale
  if (!appState.isOnline) {
    elements.offline.offlineIndicator.style.display = 'block';
  }
}

// Gestione dell'installazione PWA
function initInstallPrompt() {
  // Intercetta l'evento beforeinstallprompt
  window.addEventListener('beforeinstallprompt', (e) => {
    // Previeni il prompt automatico
    e.preventDefault();
    // Salva l'evento per usarlo più tardi
    appState.deferredPrompt = e;
    // Mostra il prompt personalizzato
    elements.install.installPrompt.classList.add('show');
  });
  
  // Listener per il pulsante di installazione
  elements.install.installBtn.addEventListener('click', () => {
    // Nascondi il prompt personalizzato
    elements.install.installPrompt.classList.remove('show');
    // Mostra il prompt nativo
    if (appState.deferredPrompt) {
      appState.deferredPrompt.prompt();
      // Attendi la scelta dell'utente
      appState.deferredPrompt.userChoice.then((choiceResult) => {
        if (choiceResult.outcome === 'accepted') {
          showToast('Grazie per aver installato la nostra app!', 'success');
        }
        appState.deferredPrompt = null;
      });
    }
  });
  
  // Listener per il pulsante "Più tardi"
  elements.install.installLaterBtn.addEventListener('click', () => {
    elements.install.installPrompt.classList.remove('show');
  });
  
  // Rileva se l'app è già installata
  window.addEventListener('appinstalled', () => {
    appState.deferredPrompt = null;
    elements.install.installPrompt.classList.remove('show');
  });
}

// Gestione della navigazione
function initNavigationListeners() {
  // Navigazione tra le viste
  elements.navigation.navServizi.addEventListener('click', (e) => {
    e.preventDefault();
    showView('serviziView');
    updateNavigation('navServizi');
  });
  
  elements.navigation.navPrenota.addEventListener('click', (e) => {
    e.preventDefault();
    if (appState.selectedServices.length > 0) {
      showView('prenotazioneView');
      updateNavigation('navPrenota');
    } else {
      showToast('Seleziona almeno un servizio prima di procedere', 'warning');
    }
  });
  
  // Pulsante per procedere alla prenotazione
  elements.services.proceedToBookingBtn.addEventListener('click', () => {
    showView('prenotazioneView');
    updateNavigation('navPrenota');
    updateBookingSummary();
  });
  
  // Pulsante per tornare ai servizi
  elements.booking.backToServicesBtn.addEventListener('click', () => {
    showView('serviziView');
    updateNavigation('navServizi');
  });
  
  // Pulsante per nuova prenotazione
  elements.confirmation.newBookingBtn.addEventListener('click', () => {
    resetApp();
    showView('serviziView');
    updateNavigation('navServizi');
  });
}

// Aggiorna la navigazione attiva
function updateNavigation(activeNavId) {
  // Rimuovi la classe active da tutti i link
  Object.values(elements.navigation).forEach(navLink => {
    navLink.classList.remove('active');
  });
  
  // Aggiungi la classe active al link corrente
  elements.navigation[activeNavId].classList.add('active');
}

// Mostra una vista specifica
function showView(viewId) {
  // Nascondi tutte le viste
  Object.values(elements.views).forEach(view => {
    view.style.display = 'none';
  });
  
  // Mostra la vista richiesta
  elements.views[viewId].style.display = 'block';
  
  // Aggiorna lo stato corrente
  appState.currentView = viewId;
  
  // Scroll to top
  window.scrollTo(0, 0);
}

// Gestione della selezione dei servizi
function initServiceSelectionListeners() {
  // Aggiungi listener a tutti gli elementi servizio
  elements.services.serviceItems.forEach(item => {
    item.addEventListener('click', () => {
      const serviceId = item.dataset.id;
      const serviceName = item.dataset.name;
      const servicePrice = parseFloat(item.dataset.price);
      
      // Verifica se il servizio è già selezionato
      const isSelected = item.classList.contains('selected');
      
      if (isSelected) {
        // Rimuovi il servizio dalla selezione
        item.classList.remove('selected');
        appState.selectedServices = appState.selectedServices.filter(s => s.id !== serviceId);
      } else {
        // Aggiungi il servizio alla selezione
        item.classList.add('selected');
        appState.selectedServices.push({
          id: serviceId,
          name: serviceName,
          price: servicePrice
        });
      }
      
      // Aggiorna il riepilogo dei servizi selezionati
      updateSelectedServices();
    });
  });
}

// Aggiorna la visualizzazione dei servizi selezionati
function updateSelectedServices() {
  // Calcola il prezzo totale
  appState.totalPrice = appState.selectedServices.reduce((total, service) => total + service.price, 0);
  
  // Aggiorna il contenitore dei servizi selezionati
  if (appState.selectedServices.length > 0) {
    elements.services.selectedServicesContainer.style.display = 'block';
    elements.services.proceedToBookingBtn.disabled = false;
    
    // Aggiorna la lista dei servizi
    elements.services.selectedServicesList.innerHTML = appState.selectedServices.map(service => `
      <div class="service-item">
        <div class="service-name">${service.name}</div>
        <div class="service-price">€${service.price.toFixed(2)}</div>
      </div>
    `).join('');
    
    // Aggiorna il prezzo totale
    elements.services.totalPrice.textContent = `€${appState.totalPrice.toFixed(2)}`;
  } else {
    elements.services.selectedServicesContainer.style.display = 'none';
    elements.services.proceedToBookingBtn.disabled = true;
  }
}

// Aggiorna il riepilogo della prenotazione
function updateBookingSummary() {
  // Aggiorna la lista dei servizi nel form di prenotazione
  elements.booking.bookingServicesList.innerHTML = appState.selectedServices.map(service => `
    <div class="service-item">
      <div class="service-name">${service.name}</div>
      <div class="service-price">€${service.price.toFixed(2)}</div>
    </div>
  `).join('');
  
  // Aggiorna il prezzo totale
  elements.booking.bookingTotalPrice.textContent = `€${appState.totalPrice.toFixed(2)}`;
}

// Inizializza i listener per il form di prenotazione
function initBookingFormListeners() {
  // Gestione del submit del form
  elements.booking.bookingForm.addEventListener('submit', (e) => {
    e.preventDefault();
    
    // Validazione del form
    if (!validateBookingForm()) {
      return;
    }
    
    // Raccogli i dati del form
    const formData = new FormData(elements.booking.bookingForm);
    const bookingData = {
      nome: formData.get('nome'),
      cognome: formData.get('cognome'),
      email: formData.get('email'),
      telefono: formData.get('telefono'),
      tipoAppuntamento: formData.get('tipoAppuntamento'),
      dataAppuntamento: formData.get('dataAppuntamento'),
      oraAppuntamento: formData.get('oraAppuntamento'),
      operatrice: formData.get('operatrice'),
      beautyTable: formData.get('beautyTable'),
      beautyRoom: formData.get('beautyRoom'),
      numeroSeduta: formData.get('numeroSeduta'),
      note: formData.get('note'),
      servizi: appState.selectedServices.map(s => s.name),
      prezzoTotale: appState.totalPrice,
      stato: 'CONFERMATO',
      timestamp: new Date().toISOString()
    };
    
    // Salva la prenotazione
    saveBooking(bookingData);
  });
  
  // Gestione dei campi data e ora
  elements.booking.dataAppuntamento.addEventListener('click', () => {
    toggleDatepicker();
  });
  
  elements.booking.oraAppuntamento.addEventListener('click', () => {
    if (appState.selectedDate) {
      toggleTimepicker();
    } else {
      showToast('Seleziona prima una data', 'warning');
    }
  });
}

// Validazione del form di prenotazione
function validateBookingForm() {
  const form = elements.booking.bookingForm;
  
  // Verifica che tutti i campi obbligatori siano compilati
  const requiredFields = form.querySelectorAll('[required]');
  let isValid = true;
  
  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      field.classList.add('error');
      isValid = false;
    } else {
      field.classList.remove('error');
    }
  });
  
  if (!isValid) {
    showToast('Compila tutti i campi obbligatori', 'error');
    return false;
  }
  
  // Validazione email
  const emailField = form.querySelector('#email');
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(emailField.value)) {
    emailField.classList.add('error');
    showToast('Inserisci un indirizzo email valido', 'error');
    return false;
  }
  
  // Validazione telefono
  const phoneField = form.querySelector('#telefono');
  const phoneRegex = /^[0-9+\s]{8,15}$/;
  if (!phoneRegex.test(phoneField.value)) {
    phoneField.classList.add('error');
    showToast('Inserisci un numero di telefono valido', 'error');
    return false;
  }
  
  return true;
}

// Inizializza i datepicker e timepicker
function initDateTimePickers() {
  // Inizializza il datepicker
  initDatepicker();
  
  // Il timepicker verrà inizializzato dopo la selezione della data
}

// Inizializza il datepicker
function initDatepicker() {
  const datepicker = elements.booking.datepicker;
  
  // Genera il calendario
  updateDatepicker(new Date());
  
  // Aggiungi listener per chiudere il datepicker quando si clicca fuori
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.datepicker-container')) {
      datepicker.style.display = 'none';
    }
  });
}

// Aggiorna il datepicker con il mese corrente
function updateDatepicker(date) {
  const datepicker = elements.booking.datepicker;
  const year = date.getFullYear();
  const month = date.getMonth();
  
  // Imposta l'intestazione del mese
  const monthNames = ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'];
  const monthYearHeader = `${monthNames[month]} ${year}`;
  
  // Calcola il primo giorno del mese
  const firstDay = new Date(year, month, 1);
  const startingDay = firstDay.getDay(); // 0 = Domenica, 1 = Lunedì, ...
  
  // Calcola il numero di giorni nel mese
  const daysInMonth = new Date(year, month + 1, 0).getDate();
  
  // Genera l'HTML del datepicker
  let datepickerHTML = `
    <div class="datepicker-header">
      <button class="datepicker-nav-btn prev-month">&lt;</button>
      <div class="datepicker-month-year">${monthYearHeader}</div>
      <button class="datepicker-nav-btn next-month">&gt;</button>
    </div>
    <div class="datepicker-weekdays">
      <div>Lun</div>
      <div>Mar</div>
      <div>Mer</div>
      <div>Gio</div>
      <div>Ven</div>
      <div>Sab</div>
      <div>Dom</div>
    </div>
    <div class="datepicker-days">
  `;
  
  // Adatta l'indice del giorno iniziale (0 = Lunedì, 6 = Domenica)
  const adjustedStartingDay = startingDay === 0 ? 6 : startingDay - 1;
  
  // Aggiungi celle vuote per i giorni prima dell'inizio del mese
  for (let i = 0; i < adjustedStartingDay; i++) {
    datepickerHTML += `<div class="datepicker-day empty"></div>`;
  }
  
  // Aggiungi i giorni del mese
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const maxDate = new Date();
  maxDate.setDate(maxDate.getDate() + CONFIG.maxFutureDays);
  
  for (let i = 1; i <= daysInMonth; i++) {
    const currentDate = new Date(year, month, i);
    const isToday = currentDate.getTime() === today.getTime();
    const isPast = currentDate < today;
    const isFuture = currentDate > maxDate;
    const isDisabled = isPast || isFuture || !isBusinessDay(currentDate);
    const isSelected = appState.selectedDate && 
                      currentDate.getFullYear() === appState.selectedDate.getFullYear() &&
                      currentDate.getMonth() === appState.selectedDate.getMonth() &&
                      currentDate.getDate() === appState.selectedDate.getDate();
    
    let classes = 'datepicker-day';
    if (isToday) classes += ' today';
    if (isSelected) classes += ' selected';
    if (isDisabled) classes += ' disabled';
    
    datepickerHTML += `
      <div class="${classes}" data-date="${year}-${month + 1}-${i}" ${isDisabled ? 'disabled' : ''}>
        ${i}
      </div>
    `;
  }
  
  datepickerHTML += `</div>`;
  
  // Aggiorna il contenuto del datepicker
  datepicker.innerHTML = datepickerHTML;
  
  // Aggiungi listener per i pulsanti di navigazione
  datepicker.querySelector('.prev-month').addEventListener('click', () => {
    const prevMonth = new Date(year, month - 1, 1);
    updateDatepicker(prevMonth);
  });
  
  datepicker.querySelector('.next-month').addEventListener('click', () => {
    const nextMonth = new Date(year, month + 1, 1);
    updateDatepicker(nextMonth);
  });
  
  // Aggiungi listener per la selezione dei giorni
  datepicker.querySelectorAll('.datepicker-day:not(.empty):not(.disabled)').forEach(day => {
    day.addEventListener('click', () => {
      // Rimuovi la selezione precedente
      datepicker.querySelectorAll('.datepicker-day.selected').forEach(selected => {
        selected.classList.remove('selected');
      });
      
      // Aggiungi la selezione al giorno corrente
      day.classList.add('selected');
      
      // Estrai la data dal data-attribute
      const [y, m, d] = day.dataset.date.split('-').map(Number);
      appState.selectedDate = new Date(y, m - 1, d);
      
      // Aggiorna il campo input
      const formattedDate = formatDate(appState.selectedDate);
      elements.booking.dataAppuntamento.value = formattedDate;
      
      // Chiudi il datepicker
      datepicker.style.display = 'none';
      
      // Resetta l'ora selezionata
      appState.selectedTime = null;
      elements.booking.oraAppuntamento.value = '';
      
      // Inizializza il timepicker per la data selezionata
      initTimepicker(appState.selectedDate);
    });
  });
}

// Verifica se una data è un giorno lavorativo
function isBusinessDay(date) {
  const day = date.getDay(); // 0 = Domenica, 1 = Lunedì, ...
  return CONFIG.orariApertura[day] !== null;
}

// Inizializza il timepicker per una data specifica
function initTimepicker(date) {
  const timepicker = elements.booking.timepicker;
  
  // Ottieni gli orari di apertura per il giorno selezionato
  const day = date.getDay();
  const hours = CONFIG.orariApertura[day];
  
  if (!hours) {
    showToast('Il centro è chiuso in questa data', 'warning');
    return;
  }
  
  // Genera gli slot orari disponibili
  const slots = generateTimeSlots(hours.start, hours.end, CONFIG.slotDuration);
  
  // Genera l'HTML del timepicker
  let timepickerHTML = `<div class="timepicker-slots">`;
  
  slots.forEach(slot => {
    const isAvailable = checkSlotAvailability(date, slot);
    const isSelected = appState.selectedTime === slot;
    
    let classes = 'timepicker-slot';
    if (isSelected) classes += ' selected';
    if (!isAvailable) classes += ' disabled';
    
    timepickerHTML += `
      <div class="${classes}" data-time="${slot}" ${!isAvailable ? 'disabled' : ''}>
        ${slot}
      </div>
    `;
  });
  
  timepickerHTML += `</div>`;
  
  // Aggiorna il contenuto del timepicker
  timepicker.innerHTML = timepickerHTML;
  
  // Aggiungi listener per la selezione degli slot
  timepicker.querySelectorAll('.timepicker-slot:not(.disabled)').forEach(slot => {
    slot.addEventListener('click', () => {
      // Rimuovi la selezione precedente
      timepicker.querySelectorAll('.timepicker-slot.selected').forEach(selected => {
        selected.classList.remove('selected');
      });
      
      // Aggiungi la selezione allo slot corrente
      slot.classList.add('selected');
      
      // Aggiorna lo stato
      appState.selectedTime = slot.dataset.time;
      
      // Aggiorna il campo input
      elements.booking.oraAppuntamento.value = appState.selectedTime;
      
      // Chiudi il timepicker
      timepicker.style.display = 'none';
    });
  });
}

// Genera gli slot orari disponibili
function generateTimeSlots(startTime, endTime, duration) {
  const slots = [];
  
  // Converti gli orari in minuti
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);
  
  const startMinutes = startHour * 60 + startMinute;
  const endMinutes = endHour * 60 + endMinute;
  
  // Genera gli slot
  for (let time = startMinutes; time < endMinutes; time += duration) {
    const hour = Math.floor(time / 60);
    const minute = time % 60;
    
    const formattedHour = hour.toString().padStart(2, '0');
    const formattedMinute = minute.toString().padStart(2, '0');
    
    slots.push(`${formattedHour}:${formattedMinute}`);
  }
  
  return slots;
}

// Verifica la disponibilità di uno slot orario
function checkSlotAvailability(date, time) {
  // In una versione reale, qui verificheremmo le prenotazioni esistenti
  // Per ora, simuliamo alcuni slot occupati
  
  // Converti data e ora in timestamp
  const [hour, minute] = time.split(':').map(Number);
  const timestamp = new Date(date);
  timestamp.setHours(hour, minute, 0, 0);
  
  // Simula alcuni slot occupati (ogni terzo slot)
  const randomOccupied = Math.random() > 0.7;
  
  return !randomOccupied;
}

// Mostra/nascondi il datepicker
function toggleDatepicker() {
  const datepicker = elements.booking.datepicker;
  
  if (datepicker.style.display === 'none' || !datepicker.style.display) {
    datepicker.style.display = 'block';
    
    // Se non c'è una data selezionata, inizializza con la data corrente
    if (!appState.selectedDate) {
      updateDatepicker(new Date());
    }
  } else {
    datepicker.style.display = 'none';
  }
}

// Mostra/nascondi il timepicker
function toggleTimepicker() {
  const timepicker = elements.booking.timepicker;
  
  if (timepicker.style.display === 'none' || !timepicker.style.display) {
    timepicker.style.display = 'block';
  } else {
    timepicker.style.display = 'none';
  }
}

// Salva una prenotazione
function saveBooking(bookingData) {
  // Se online, invia direttamente ad Airtable
  if (appState.isOnline) {
    sendBookingToAirtable(bookingData)
      .then(() => {
        // Mostra la conferma
        showBookingConfirmation(bookingData);
      })
      .catch(error => {
        console.error('Errore durante l\'invio della prenotazione:', error);
        // Salva localmente in caso di errore
        saveBookingLocally(bookingData);
        showToast('Errore durante l\'invio della prenotazione. Sarà sincronizzata quando possibile.', 'error');
      });
  } else {
    // Se offline, salva localmente
    saveBookingLocally(bookingData);
    // Mostra la conferma
    showBookingConfirmation(bookingData);
  }
}

// Invia una prenotazione ad Airtable
function sendBookingToAirtable(bookingData) {
  // In una versione reale, qui invieremmo i dati ad Airtable
  // Per ora, simuliamo una chiamata API
  
  return new Promise((resolve, reject) => {
    // Simula una chiamata API con un ritardo
    setTimeout(() => {
      // Simula un successo o un errore casuale
      const success = Math.random() > 0.2;
      
      if (success) {
        resolve({ id: 'rec' + Math.random().toString(36).substr(2, 9) });
      } else {
        reject(new Error('Errore di connessione al server'));
      }
    }, 1500);
  });
}

// Salva una prenotazione localmente
function saveBookingLocally(bookingData) {
  // Aggiungi la prenotazione alla lista delle prenotazioni in attesa
  appState.pendingBookings.push(bookingData);
  
  // Salva nella localStorage
  localStorage.setItem('pendingBookings', JSON.stringify(appState.pendingBookings));
  
  showToast('Prenotazione salvata localmente. Sarà sincronizzata quando tornerai online.', 'info');
}

// Sincronizza le prenotazioni in attesa
function syncPendingBookings() {
  if (appState.pendingBookings.length === 0) {
    return;
  }
  
  showToast(`Sincronizzazione di ${appState.pendingBookings.length} prenotazioni in corso...`, 'info');
  
  // Copia le prenotazioni in attesa
  const pendingBookings = [...appState.pendingBookings];
  
  // Resetta la lista
  appState.pendingBookings = [];
  localStorage.removeItem('pendingBookings');
  
  // Invia ogni prenotazione
  const promises = pendingBookings.map(booking => sendBookingToAirtable(booking));
  
  Promise.allSettled(promises)
    .then(results => {
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;
      
      if (failed > 0) {
        // Rimetti le prenotazioni fallite nella lista
        results.forEach((result, index) => {
          if (result.status === 'rejected') {
            appState.pendingBookings.push(pendingBookings[index]);
          }
        });
        
        // Salva nella localStorage
        localStorage.setItem('pendingBookings', JSON.stringify(appState.pendingBookings));
        
        showToast(`Sincronizzate ${successful} prenotazioni. ${failed} prenotazioni non sincronizzate.`, 'warning');
      } else {
        showToast(`Tutte le ${successful} prenotazioni sono state sincronizzate con successo!`, 'success');
      }
    });
}

// Verifica se ci sono prenotazioni in attesa
function checkPendingBookings() {
  // Carica le prenotazioni in attesa dalla localStorage
  const pendingBookings = localStorage.getItem('pendingBookings');
  
  if (pendingBookings) {
    appState.pendingBookings = JSON.parse(pendingBookings);
    
    if (appState.pendingBookings.length > 0) {
      showToast(`Hai ${appState.pendingBookings.length} prenotazioni in attesa di sincronizzazione.`, 'info');
      
      // Se online, tenta di sincronizzare
      if (appState.isOnline) {
        syncPendingBookings();
      }
    }
  }
}

// Mostra la conferma della prenotazione
function showBookingConfirmation(bookingData) {
  // Aggiorna i dettagli della conferma
  elements.confirmation.confirmationEmail.textContent = bookingData.email;
  elements.confirmation.confirmationDate.textContent = bookingData.dataAppuntamento;
  elements.confirmation.confirmationTime.textContent = bookingData.oraAppuntamento;
  elements.confirmation.confirmationOperator.textContent = bookingData.operatrice;
  
  // Mostra la vista di conferma
  showView('confermaView');
  
  // Invia email di conferma
  if (appState.isOnline) {
    sendConfirmationEmail(bookingData);
  }
}

// Invia email di conferma
function sendConfirmationEmail(bookingData) {
  // In una versione reale, qui invieremmo l'email
  // Per ora, simuliamo l'invio
  
  console.log('Invio email di conferma a:', bookingData.email);
  
  // Simula un ritardo
  setTimeout(() => {
    console.log('Email inviata con successo');
  }, 1000);
}

// Carica i dati dalla cache locale
function loadCachedData() {
  // Carica i servizi
  const cachedServices = localStorage.getItem('services');
  if (cachedServices) {
    dataCache.services = JSON.parse(cachedServices);
  }
  
  // Carica le prenotazioni
  const cachedBookings = localStorage.getItem('bookings');
  if (cachedBookings) {
    dataCache.bookings = JSON.parse(cachedBookings);
  }
  
  // Carica l'utente corrente
  const currentUser = localStorage.getItem('currentUser');
  if (currentUser) {
    appState.currentUser = JSON.parse(currentUser);
  }
}

// Resetta l'app per una nuova prenotazione
function resetApp() {
  // Resetta i servizi selezionati
  appState.selectedServices = [];
  appState.totalPrice = 0;
  
  // Resetta la selezione visiva
  elements.services.serviceItems.forEach(item => {
    item.classList.remove('selected');
  });
  
  // Aggiorna la visualizzazione
  updateSelectedServices();
  
  // Resetta il form
  elements.booking.bookingForm.reset();
  
  // Resetta data e ora
  appState.selectedDate = null;
  appState.selectedTime = null;
}

// Formatta una data in formato leggibile
function formatDate(date) {
  const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
  return date.toLocaleDateString('it-IT', options);
}

// Mostra un toast di notifica
function showToast(message, type = 'info') {
  const toastContainer = elements.toastContainer;
  
  // Crea l'elemento toast
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;
  
  // Icona in base al tipo
  let icon = '';
  switch (type) {
    case 'success':
      icon = '<i class="fas fa-check-circle toast-icon"></i>';
      break;
    case 'error':
      icon = '<i class="fas fa-exclamation-circle toast-icon"></i>';
      break;
    case 'warning':
      icon = '<i class="fas fa-exclamation-triangle toast-icon"></i>';
      break;
    case 'info':
    default:
      icon = '<i class="fas fa-info-circle toast-icon"></i>';
      break;
  }
  
  // Contenuto del toast
  toast.innerHTML = `
    ${icon}
    <div class="toast-message">${message}</div>
    <button class="toast-close">&times;</button>
  `;
  
  // Aggiungi il toast al container
  toastContainer.appendChild(toast);
  
  // Aggiungi listener per chiudere il toast
  toast.querySelector('.toast-close').addEventListener('click', () => {
    toast.remove();
  });
  
  // Rimuovi automaticamente il toast dopo 5 secondi
  setTimeout(() => {
    if (toast.parentNode) {
      toast.remove();
    }
  }, 5000);
}
