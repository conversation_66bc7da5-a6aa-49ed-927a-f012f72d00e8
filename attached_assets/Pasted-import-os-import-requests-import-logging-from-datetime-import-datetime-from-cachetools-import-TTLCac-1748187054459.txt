import os
import requests
import logging
from datetime import datetime
from cachetools import TTLCache
from typing import List, Dict, Optional

# Configurazione del logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


class AirtableClient:

    def __init__(self):
        self.api_key = os.environ.get('AIRTABLE_API_KEY')
        self.base_id = "appbZ4ONNnQp69VtM"  # Base ID aggiornato
        self.api_url = f'https://api.airtable.com/v0/{self.base_id}'
        self.cache = TTLCache(maxsize=100, ttl=300)  # Cache for 5 minutes
        self.page_size = 4  # Ridotto per testare la paginazione

        # Per debugging, stampiamo l'URL di base e il prefisso della API key
        if self.api_key:
            api_key_preview = self.api_key[:10] + '...' if len(
                self.api_key) > 10 else self.api_key
            logger.info(
                f"Airtable client initialized with base ID: {self.base_id}")
            logger.info(f"API key (first few chars): {api_key_preview}")

    def _get_headers(self):
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        if self.api_key:
            logger.debug(
                f"Using API key (first few chars): {self.api_key[:10]}...")
        else:
            logger.warning("API key is not set")
        return headers

    def get_appointments(self,
                         offset: Optional[str] = None,
                         status: Optional[str] = None) -> Dict:
        """
        Get appointments with pagination support using offset and optional filtering by status
        """
        try:
            # Inizializza i parametri
            params = {}

            # pageSize deve essere un numero intero
            params['pageSize'] = self.page_size

            # offset è fornito come stringa dalla risposta API e deve rimanere stringa
            if offset:
                params['offset'] = offset

            # Filtro per status (per ottimizzare il caricamento dei dati)
            if status:
                # Converte lo status al formato Airtable e crea la formula di filtro
                status_formula = ""

                if status.lower() == 'richiesti':
                    # Include sia 'RICHIESTO' che 'IN ATTESA'
                    status_formula = "OR({STATO}='RICHIESTO',{STATO}='IN ATTESA')"
                elif status.lower() == 'confermati':
                    status_formula = "{STATO}='CONFERMATO'"
                elif status.lower() == 'cancellati':
                    status_formula = "{STATO}='CANCELLATO'"

                if status_formula:
                    params['filterByFormula'] = status_formula
                    logger.debug(
                        f"Filtering appointments with formula: {status_formula}"
                    )

            request_url = f'{self.api_url}/tblAanKl9ORXkyFEr'
            logger.debug(f"Making GET request to: {request_url}")
            logger.debug(f"With params: {params}")

            response = requests.get(request_url,
                                    headers=self._get_headers(),
                                    params=params)

            response.raise_for_status()
            data = response.json()
            records = data.get('records', [])
            processed_appointments = []

            for appointment in records:
                fields = appointment['fields']

                # Estrai i servizi
                service = "Nessun servizio"
                if 'Servizi (from Servizi)' in fields:
                    services = fields['Servizi (from Servizi)']
                    if isinstance(services, list) and services:
                        service = services[0]
                    elif isinstance(services, str):
                        service = services

                # Estrai l'operatore
                operator = "Non assegnato"
                if 'Operatrici' in fields:
                    operators = fields['Operatrici']
                    if isinstance(operators, list) and operators:
                        operator = operators[0]
                    elif isinstance(operators, str):
                        operator = operators

                # Debug raw status value
                raw_status = fields.get('STATO', 'RICHIESTO')
                logger.debug(
                    f"Raw status value for {appointment['id']}: '{raw_status}', Type: {type(raw_status)}"
                )

                processed_appointment = {
                    'id':
                    appointment['id'],
                    'client_name':
                    self._get_client_full_name(fields),
                    'service':
                    service,
                    'operator':
                    operator,
                    'datetime':
                    fields.get('Data Appuntamento', ''),
                    'status':
                    raw_status,
                    'phone':
                    fields.get('Telefono (from Nome)', [''])[0] if isinstance(
                        fields.get('Telefono (from Nome)'), list) else
                    fields.get('Telefono (from Nome)', '')
                }
                processed_appointments.append(processed_appointment)
                logger.debug(f"Processed appointment: {processed_appointment}")

            # Sort appointments by datetime
            processed_appointments.sort(key=lambda x: x['datetime'],
                                        reverse=True)

            return {
                'records': processed_appointments,
                'offset': data.get('offset')
            }
        except Exception as e:
            logger.error(f"Error in get_appointments: {str(e)}")
            raise

    def get_statistics(self) -> Dict:
        """
        Get appointment statistics
        """
        try:
            all_appointments = []
            offset = None

            # Raccogliamo tutti gli appuntamenti usando la paginazione
            while True:
                result = self.get_appointments(offset=offset)
                all_appointments.extend(result['records'])
                offset = result.get('offset')
                if not offset:
                    break

            # Calcoliamo le statistiche
            total = len(all_appointments)
            confirmed = sum(1 for a in all_appointments
                            if a['status'].lower() == 'confermato')
            pending = sum(1 for a in all_appointments
                          if a['status'].lower() == 'richiesto')
            cancelled = sum(1 for a in all_appointments
                            if a['status'].lower() == 'cancellato')

            # Debug log per verificare il conteggio
            logger.debug(f"Total appointments: {total}")
            logger.debug(
                f"Status breakdown - Confirmed: {confirmed}, Pending: {pending}, Cancelled: {cancelled}"
            )
            # Log più dettagliato degli stati
            status_counts = {}
            for app in all_appointments:
                status = app['status']
                status_counts[status] = status_counts.get(status, 0) + 1

            logger.debug(f"Detailed status counts: {status_counts}")
            logger.debug(
                f"Sample statuses: {[a['status'] for a in all_appointments[:5]]}"
            )

            return {
                'total_appointments': total,
                'confirmed': confirmed,
                'pending': pending,
                'cancelled': cancelled
            }

        except Exception as e:
            logger.error(f"Error in get_statistics: {str(e)}")
            raise

    def _get_client_full_name(self, fields: Dict) -> str:
        nome = fields.get('Nome (from Nome)', [''])[0] if isinstance(
            fields.get('Nome (from Nome)'), list) else ''
        cognome = fields.get('Cognome (from Nome)', [''])[0] if isinstance(
            fields.get('Cognome (from Nome)'), list) else ''
        nome_app = fields.get('NOME DA APP', '')

        if nome_app:
            return nome_app
        elif nome or cognome:
            return f"{nome} {cognome}".strip()
        else:
            return "Cliente senza nome"

    def get_operators(self) -> List[str]:
        operators = [
            'Francesca Cotugno', 'Mena Sardo', 'Viola Basile',
            'Marcella Illiano', 'Elena Beauty', 'Marzia Fascelli',
            'Giorgia Lucignano'
        ]
        return operators

    def get_services(self) -> List[str]:
        """
        Get the list of available services
        """
        cache_key = 'services_list'

        if cache_key in self.cache:
            return self.cache[cache_key]

        services = [
            'Manicure', 'Pedicure', 'Ricostruzione unghie',
            'Semipermanente mani', 'Semipermanente piedi', 'Massaggio',
            'Pulizia viso', 'Trattamento viso', 'Ceretta', 'Epilazione laser',
            'Trucco', 'Bendaggi + massaggio 30 min.', 'Copertura in gel',
            'Baffetto e sopracciglia', 'Rimozione semipermanente piedi'
        ]

        self.cache[cache_key] = services
        return services

    def create_appointment(self, data: Dict) -> Dict:
        """
        Create a new appointment in Airtable
        """
        try:
            # Prendiamo i dati direttamente dal form
            client_name = data.get("client_name", "")
            phone = data.get("phone", "")
            service = data.get("service", "")
            operator = data.get("operator", "")
            datetime_str = data.get("datetime", "")
            notes = data.get("notes", "")

            logger.debug(
                f"Received appointment data: name={client_name}, phone={phone}, service={service}, operator={operator}, datetime={datetime_str}"
            )

            # Creiamo i campi secondo la struttura richiesta da Airtable
            fields = {
                "NOME DA APP": client_name,  # Usa il nome dal form
                "Telefono": phone,  # Usa il telefono dal form
                "TIPO APPUNTAMENTO": "TRATTAMENTO",
                "TRATTAMENTO": service,  # Usa il servizio selezionato nel form
                "Operatrici": operator,  # Usa l'operatore selezionato nel form
                "Data Appuntamento":
                datetime_str,  # Usa la data e ora dal form
                "Beauty Room":
                "01 - AFRODITE",  # Valore di default per la stanza
                "STATO":
                "RICHIESTO",  # Stato fisso RICHIESTO per nuovi appuntamenti
                "NOTE": notes  # Note dal form
            }

            # Se ci sono dati nel payload fornito, li usiamo per sovrascrivere i valori
            if "fields" in data:
                for key, value in data["fields"].items():
                    fields[key] = value

            payload = {"records": [{"fields": fields}]}

            logger.debug(f"Creating appointment with payload: {payload}")

            request_url = f'{self.api_url}/tblAanKl9ORXkyFEr'
            response = requests.post(request_url,
                                     headers=self._get_headers(),
                                     json=payload)

            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error in create_appointment: {str(e)}")
            raise

    def update_appointment_status(self, appointment_id: str,
                                  status: str) -> Dict:
        """
        Update the status of an appointment
        """
        try:
            payload = {"fields": {"STATO": status.upper()}}

            response = requests.patch(
                f'{self.api_url}/tblAanKl9ORXkyFEr/{appointment_id}',
                headers=self._get_headers(),
                json=payload)
            response.raise_for_status()

            # Invalida la cache per forzare una rilettura degli appuntamenti
            for key in list(self.cache.keys()):
                if key.startswith('appointments_'):
                    del self.cache[key]

            return response.json()
        except Exception as e:
            raise Exception(f"Error updating appointment status: {str(e)}")

    def get_raw_clients(self, offset: Optional[str] = None) -> Dict:
        """
        Get all clients from Airtable (Anagrafica Clienti) without processing,
        with paging support using offset.
        """
        try:
            # Rimuovo il parametro view per prendere tutti i clienti
            params = {'pageSize': 100}  # pageSize deve essere numerico

            # Aggiungo l'offset se fornito per supportare la paginazione
            if offset:
                params[
                    'offset'] = offset  # offset è una stringa dall'API di Airtable

            # Log della richiesta HTTP
            request_url = f'{self.api_url}/Anagrafica%20Clienti'
            logger.debug(f"Making GET request to: {request_url}")
            logger.debug(f"With params: {params}")
            logger.info(f"Fetching clients with params: {params}")

            # Aggiungo l'API key al log per verifica (solo i primi caratteri)
            api_key = self._get_headers().get('Authorization', '')
            if api_key:
                api_key_preview = api_key[:10] + '...' if len(
                    api_key) > 10 else api_key
                logger.debug(
                    f"Using API key (first few chars): {api_key_preview}")

            response = requests.get(request_url,
                                    headers=self._get_headers(),
                                    params=params)

            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error in get_raw_clients: {str(e)}")
            raise

    def create_client(self, data: Dict) -> Dict:
        """
        Create a new client in Airtable (Anagrafica Clienti)
        """
        try:
            # Creiamo i campi secondo la struttura richiesta da Airtable
            fields = {
                "Nome": data.get("nome", ""),
                "Cognome": data.get("cognome", ""),
                "Telefono": data.get("telefono", ""),
                "Email": data.get("email", ""),
                "Data di nascita": data.get("dataNascita", ""),
                "Note": data.get("note", ""),
                "Note CORPO": data.get("noteCorpo", ""),
                "Note CORPO 2": data.get("noteCorpo2", ""),
                "Note CORPO 3": data.get("noteCorpo3", ""),
                "Note VISO": data.get("noteViso", ""),
                "Pelle": [data.get("pelle", "")] if data.get("pelle") else [],
                "Allergie": data.get("allergie", ""),
                "TRATTAMENTO": data.get("trattamento", ""),
                "Creme usate": data.get("cremeUsate", ""),
                "Cure in corso": data.get("cureInCorso", "")
            }

            # Rimuoviamo il campo ID perché sarà generato automaticamente da Airtable
            # L'ID specifico causa problemi di validazione lato Airtable (errore 422)

            payload = {"records": [{"fields": fields}]}

            logger.debug(f"Creating client with payload: {payload}")

            request_url = f'{self.api_url}/Anagrafica%20Clienti'
            response = requests.post(request_url,
                                     headers=self._get_headers(),
                                     json=payload)

            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error in create_client: {str(e)}")
            raise

    def update_client(self, client_id: str, data: Dict) -> Dict:
        """
        Update an existing client in Airtable (Anagrafica Clienti)
        """
        try:
            # Creiamo i campi secondo la struttura richiesta da Airtable
            fields = {
                "Nome": data.get("nome", ""),
                "Cognome": data.get("cognome", ""),
                "Telefono": data.get("telefono", ""),
                "Email": data.get("email", ""),
                "Data di nascita": data.get("dataNascita", ""),
                "Note": data.get("note", ""),
                "Note CORPO": data.get("noteCorpo", ""),
                "Note CORPO 2": data.get("noteCorpo2", ""),
                "Note CORPO 3": data.get("noteCorpo3", ""),
                "Note VISO": data.get("noteViso", ""),
                "Pelle": [data.get("pelle", "")] if data.get("pelle") else [],
                "Allergie": data.get("allergie", ""),
                "TRATTAMENTO": data.get("trattamento", ""),
                "Creme usate": data.get("cremeUsate", ""),
                "Cure in corso": data.get("cureInCorso", "")
            }

            # Filtriamo campi vuoti per evitare di sovrascrivere dati esistenti con valori vuoti
            fields = {k: v for k, v in fields.items() if v}

            payload = {"records": [{"id": client_id, "fields": fields}]}

            logger.debug(f"Updating client with payload: {payload}")

            request_url = f'{self.api_url}/Anagrafica%20Clienti'
            response = requests.patch(request_url,
                                      headers=self._get_headers(),
                                      json=payload)

            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error in update_client: {str(e)}")
            raise

    def get_clients(self) -> List[Dict]:
        """
        Get all clients from Airtable
        """
        try:
            params = {'pageSize': self.page_size}

            # Log della richiesta HTTP
            request_url = f'{self.api_url}/tblAanKl9ORXkyFEr'
            logger.debug(f"Making GET request to: {request_url}")
            logger.debug(f"With params: {params}")

            response = requests.get(request_url,
                                    headers=self._get_headers(),
                                    params=params)

            response.raise_for_status()
            data = response.json()

            clients = []
            for record in data.get('records', []):
                fields = record['fields']
                client = {
                    'id':
                    record['id'],
                    'nome':
                    fields.get('Nome (from Nome)', [''])[0] if isinstance(
                        fields.get('Nome (from Nome)'), list) else '',
                    'cognome':
                    fields.get('Cognome (from Nome)', [''])[0] if isinstance(
                        fields.get('Cognome (from Nome)'), list) else '',
                    'telefono':
                    fields.get('Telefono (from Nome)', [''])[0] if isinstance(
                        fields.get('Telefono (from Nome)'), list) else '',
                    'email':
                    fields.get('Email (from Nome)', [''])[0] if isinstance(
                        fields.get('Email (from Nome)'), list) else '',
                    'total_treatments':
                    len(fields.get('SERVIZI DA APP', [])),
                    'last_appointment':
                    fields.get('Data Appuntamento', '')
                }
                clients.append(client)

            return clients
        except Exception as e:
            logger.error(f"Error in get_clients: {str(e)}")
            raise

    def search_clients(self, query: str) -> List[Dict]:
        """
        Search clients by name or surname
        """
        try:
            clients = self.get_clients()
            return [
                client for client in clients
                if query in client['nome'].lower()
                or query in client['cognome'].lower()
            ]
        except Exception as e:
            logger.error(f"Error in search_clients: {str(e)}")
            raise

    def get_client(self, client_id: str) -> Optional[Dict]:
        """
        Get detailed information for a specific client
        """
        try:
            logger.debug(f"Fetching client details for ID: {client_id}")
            response = requests.get(
                f'{self.api_url}/Anagrafica%20Clienti/{client_id}',
                headers=self._get_headers())
            response.raise_for_status()

            record = response.json()
            fields = record['fields']
            logger.debug(f"Raw client fields from Airtable: {fields}")

            # Get all appointments for this client to calculate statistics
            appointments = self.get_appointments()
            client_name = self._get_client_full_name(fields)
            client_appointments = []
            for app in appointments['records']:
                if 'client_name' in app and app['client_name'] == client_name:
                    appointment_data = {
                        'datetime': app.get('datetime', ''),
                        'service': app.get('service', 'Non specificato'),
                        'operator': app.get('operator', 'Non specificato'),
                        'status': app.get('status', 'pending')
                    }
                    client_appointments.append(appointment_data)

            # Calculate favorite treatment and operator
            treatments_count = {}
            operators_count = {}
            for app in client_appointments:
                if app['service']:
                    treatments_count[app['service']] = treatments_count.get(
                        app['service'], 0) + 1
                if app['operator']:
                    operators_count[app['operator']] = operators_count.get(
                        app['operator'], 0) + 1

            favorite_treatment = max(
                treatments_count.items(),
                key=lambda x: x[1]) if treatments_count else ('N/A', 0)
            favorite_operator = max(
                operators_count.items(),
                key=lambda x: x[1]) if operators_count else ('N/A', 0)

            # Calculate average frequency (treatments per month)
            if len(client_appointments) > 1:
                # Filtra solo date valide
                valid_dates = [
                    app['datetime'] for app in client_appointments
                    if app['datetime']
                ]
                if len(valid_dates) > 1:
                    first_date = min(valid_dates)
                    last_date = max(valid_dates)

                    try:
                        # Gestisce diversi formati di data
                        try:
                            first_date_obj = datetime.strptime(
                                first_date, '%Y-%m-%dT%H:%M:%S.%fZ')
                        except ValueError:
                            first_date_obj = datetime.strptime(
                                first_date, '%Y-%m-%dT%H:%M:%S.%f%z')

                        try:
                            last_date_obj = datetime.strptime(
                                last_date, '%Y-%m-%dT%H:%M:%S.%fZ')
                        except ValueError:
                            last_date_obj = datetime.strptime(
                                last_date, '%Y-%m-%dT%H:%M:%S.%f%z')

                        date_diff = last_date_obj - first_date_obj
                        months_diff = date_diff.days / 30

                        # Se la differenza è meno di un mese, mostra il numero di trattamenti
                        if months_diff < 1:
                            avg_frequency = len(client_appointments)
                        else:
                            avg_frequency = round(
                                len(client_appointments) / months_diff, 1)
                    except Exception as e:
                        logger.error(
                            f"Error calculating date difference: {str(e)}")
                        avg_frequency = len(client_appointments)
                else:
                    avg_frequency = len(client_appointments)
            else:
                avg_frequency = len(client_appointments)

            # Prepare treatments data for the chart
            treatments_data = {
                'labels': list(treatments_count.keys()),
                'data': list(treatments_count.values())
            }

            # Format dates for recent treatments
            recent_treatments = []
            for app in sorted(client_appointments,
                              key=lambda x: x['datetime'],
                              reverse=True)[:5]:
                treatment = {
                    'date': app['datetime'],
                    'service': app['service'],
                    'operator': app['operator'],
                    'status': app['status'],
                    'status_class': self._get_status_class(app['status'])
                }
                recent_treatments.append(treatment)

            client = {
                'id':
                record['id'],
                'nome':
                fields.get('Nome (from Nome)', [''])[0] if isinstance(
                    fields.get('Nome (from Nome)'), list) else '',
                'cognome':
                fields.get('Cognome (from Nome)', [''])[0] if isinstance(
                    fields.get('Cognome (from Nome)'), list) else '',
                'created_at':
                record['createdTime'],
                'total_treatments':
                len(client_appointments),
                'favorite_treatment':
                favorite_treatment[0],
                'favorite_treatment_count':
                favorite_treatment[1],
                'favorite_operator':
                favorite_operator[0],
                'favorite_operator_count':
                favorite_operator[1],
                'average_frequency':
                round(avg_frequency, 1),
                'last_appointment':
                client_appointments[0]['datetime']
                if client_appointments else None,
                'treatments_data':
                treatments_data,
                'recent_treatments':
                recent_treatments
            }

            logger.debug(f"Processed client data: {client}")
            return client
        except Exception as e:
            logger.error(f"Error in get_client: {str(e)}")
            raise

    def _get_status_class(self, status: str) -> str:
        """
        Get the CSS class for a status
        """
        status_map = {
            'richiesto': 'bg-yellow-100 text-yellow-800',
            'confermato': 'bg-green-100 text-green-800',
            'cancellato': 'bg-red-100 text-red-800'
        }
        return status_map.get(status.lower(), 'bg-gray-100 text-gray-800')
