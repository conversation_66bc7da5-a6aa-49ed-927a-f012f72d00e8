// airtable-api.js - Gestione delle chiamate API ad Airtable

// Configurazione API Airtable
const AIRTABLE_CONFIG = {
  baseId: 'appbZ4ONNnQp69VtM',
  tableName: 'DASHBOARD',
  apiUrl: 'https://api.airtable.com/v0',
  apiKey: '' // Da impostare in fase di produzione
};

/**
 * Classe per la gestione delle chiamate API ad Airtable
 */
class AirtableAPI {
  constructor(config) {
    this.baseId = config.baseId;
    this.tableName = config.tableName;
    this.apiUrl = config.apiUrl;
    this.apiKey = config.apiKey;
    this.isConfigured = false;
  }

  /**
   * Configura l'API con la chiave API
   * @param {string} apiKey - La chiave API di Airtable
   */
  configure(apiKey) {
    this.apiKey = apiKey;
    this.isConfigured = true;
    console.log('Airtable API configurata con successo');
  }

  /**
   * Verifica se l'API è configurata
   * @returns {boolean} - True se l'API è configurata, false altrimenti
   */
  checkConfiguration() {
    if (!this.isConfigured) {
      console.warn('Airtable API non configurata. Utilizzare il metodo configure() prima di effettuare chiamate API.');
      return false;
    }
    return true;
  }

  /**
   * Crea un nuovo record nella tabella Airtable
   * @param {Object} data - I dati da inserire
   * @returns {Promise} - Promise con il risultato della chiamata
   */
  async createRecord(data) {
    if (!this.checkConfiguration()) {
      return Promise.reject(new Error('API non configurata'));
    }

    try {
      // Prepara i dati nel formato richiesto da Airtable
      const formattedData = this.formatDataForAirtable(data);

      // Effettua la chiamata API
      const response = await fetch(`${this.apiUrl}/${this.baseId}/${this.tableName}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fields: formattedData
        })
      });

      // Verifica la risposta
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Errore Airtable: ${errorData.error?.message || 'Errore sconosciuto'}`);
      }

      // Restituisci i dati della risposta
      return await response.json();
    } catch (error) {
      console.error('Errore durante la creazione del record:', error);
      throw error;
    }
  }

  /**
   * Formatta i dati nel formato richiesto da Airtable
   * @param {Object} data - I dati da formattare
   * @returns {Object} - I dati formattati
   */
  formatDataForAirtable(data) {
    // Mappa i campi del form ai campi di Airtable
    const formattedData = {
      // Campi principali
      "Nome": [data.nomeId], // Riferimento al record Nome
      "TIPO APPUNTAMENTO": data.tipoAppuntamento,
      "TRATTAMENTO": data.trattamento || "TRATTAMENTO GENERICO",
      "Data Appuntamento": this.formatDateForAirtable(data.dataAppuntamento, data.oraAppuntamento),
      "Servizi": data.serviziIds || [], // Array di ID dei servizi
      "SERVIZI DA APP": data.servizi || [], // Array di nomi dei servizi
      "Operatrici": data.operatrice,
      "Beauty Table": data.beautyTable || "",
      "Beauty Room": data.beautyRoom || "",
      "STATO": "CONFERMATO",
      "N° SEDUTA": data.numeroSeduta || "SEDUTA 01"
    };

    // Aggiungi campi opzionali se presenti
    if (data.note) {
      formattedData["Note01"] = data.note;
    }

    return formattedData;
  }

  /**
   * Formatta una data e un'ora nel formato richiesto da Airtable
   * @param {string} date - La data in formato locale
   * @param {string} time - L'ora in formato HH:MM
   * @returns {string} - La data formattata per Airtable
   */
  formatDateForAirtable(date, time) {
    // Estrai la data dal formato locale (es. "Lunedì, 21 maggio 2025")
    const dateRegex = /(\d{1,2})\s+(\w+)\s+(\d{4})/;
    const dateMatch = date.match(dateRegex);
    
    if (!dateMatch) {
      console.error('Formato data non valido:', date);
      return null;
    }
    
    const day = dateMatch[1];
    const monthName = dateMatch[2].toLowerCase();
    const year = dateMatch[3];
    
    // Mappa dei nomi dei mesi in italiano
    const monthMap = {
      'gennaio': 0, 'febbraio': 1, 'marzo': 2, 'aprile': 3,
      'maggio': 4, 'giugno': 5, 'luglio': 6, 'agosto': 7,
      'settembre': 8, 'ottobre': 9, 'novembre': 10, 'dicembre': 11
    };
    
    const month = monthMap[monthName];
    
    if (month === undefined) {
      console.error('Nome mese non valido:', monthName);
      return null;
    }
    
    // Crea l'oggetto data
    const dateObj = new Date(year, month, day);
    
    // Aggiungi l'ora
    if (time) {
      const [hours, minutes] = time.split(':').map(Number);
      dateObj.setHours(hours, minutes, 0, 0);
    }
    
    // Formatta la data nel formato ISO richiesto da Airtable
    return dateObj.toISOString();
  }

  /**
   * Crea un nuovo record cliente nella tabella Nome
   * @param {Object} clientData - I dati del cliente
   * @returns {Promise} - Promise con il risultato della chiamata
   */
  async createClientRecord(clientData) {
    if (!this.checkConfiguration()) {
      return Promise.reject(new Error('API non configurata'));
    }

    try {
      // Prepara i dati nel formato richiesto da Airtable
      const formattedData = {
        "Nome": clientData.nome,
        "Cognome": clientData.cognome,
        "Email": clientData.email,
        "Telefono": clientData.telefono
      };

      // Effettua la chiamata API alla tabella Nome
      const response = await fetch(`${this.apiUrl}/${this.baseId}/Nome`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fields: formattedData
        })
      });

      // Verifica la risposta
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Errore Airtable: ${errorData.error?.message || 'Errore sconosciuto'}`);
      }

      // Restituisci i dati della risposta
      return await response.json();
    } catch (error) {
      console.error('Errore durante la creazione del record cliente:', error);
      throw error;
    }
  }

  /**
   * Verifica se un cliente esiste già nella tabella Nome
   * @param {string} email - L'email del cliente
   * @returns {Promise} - Promise con il risultato della ricerca
   */
  async findClientByEmail(email) {
    if (!this.checkConfiguration()) {
      return Promise.reject(new Error('API non configurata'));
    }

    try {
      // Effettua la chiamata API per cercare il cliente
      const response = await fetch(
        `${this.apiUrl}/${this.baseId}/Nome?filterByFormula=${encodeURIComponent(`{Email}="${email}"`)}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      // Verifica la risposta
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Errore Airtable: ${errorData.error?.message || 'Errore sconosciuto'}`);
      }

      // Restituisci i dati della risposta
      const data = await response.json();
      return data.records.length > 0 ? data.records[0] : null;
    } catch (error) {
      console.error('Errore durante la ricerca del cliente:', error);
      throw error;
    }
  }

  /**
   * Ottiene i servizi disponibili dalla tabella Servizi
   * @returns {Promise} - Promise con i servizi disponibili
   */
  async getServices() {
    if (!this.checkConfiguration()) {
      return Promise.reject(new Error('API non configurata'));
    }

    try {
      // Effettua la chiamata API per ottenere i servizi
      const response = await fetch(
        `${this.apiUrl}/${this.baseId}/Servizi`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      // Verifica la risposta
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Errore Airtable: ${errorData.error?.message || 'Errore sconosciuto'}`);
      }

      // Restituisci i dati della risposta
      const data = await response.json();
      return data.records;
    } catch (error) {
      console.error('Errore durante il recupero dei servizi:', error);
      throw error;
    }
  }
}

// Esporta l'istanza dell'API
const airtableApi = new AirtableAPI(AIRTABLE_CONFIG);
export default airtableApi;
