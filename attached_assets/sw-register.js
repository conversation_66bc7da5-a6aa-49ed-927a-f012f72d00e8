// sw-register.js - Registrazione del Service Worker

// Registra il service worker solo se supportato dal browser
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('Service Worker registrato con successo:', registration.scope);
      })
      .catch((error) => {
        console.error('Errore durante la registrazione del Service Worker:', error);
      });
  });

  // Gestione degli aggiornamenti del Service Worker
  let refreshing = false;
  navigator.serviceWorker.addEventListener('controllerchange', () => {
    if (refreshing) return;
    refreshing = true;
    window.location.reload();
  });

  // Gestione dei messaggi dal Service Worker
  navigator.serviceWorker.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SYNC_COMPLETED') {
      const { successful, failed } = event.data;
      
      if (failed > 0) {
        showToast(`Sincronizzate ${successful} prenotazioni. ${failed} prenotazioni non sincronizzate.`, 'warning');
      } else {
        showToast(`Tutte le ${successful} prenotazioni sono state sincronizzate con successo!`, 'success');
      }
    }
  });

  // Funzione per richiedere la sincronizzazione delle prenotazioni
  function requestSync() {
    if ('SyncManager' in window) {
      navigator.serviceWorker.ready
        .then((registration) => {
          return registration.sync.register('sync-bookings');
        })
        .then(() => {
          console.log('Sincronizzazione registrata');
        })
        .catch((error) => {
          console.error('Errore durante la registrazione della sincronizzazione:', error);
        });
    } else {
      console.log('Background Sync non supportato');
      // Fallback: tenta la sincronizzazione immediata
      syncPendingBookings();
    }
  }
}

// Funzione per mostrare un toast di notifica (definita globalmente per essere accessibile dal Service Worker)
function showToast(message, type = 'info') {
  if (typeof window.showToast === 'function') {
    window.showToast(message, type);
  } else {
    console.log(`Toast (${type}): ${message}`);
  }
}
