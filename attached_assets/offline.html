<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Aphrodite Center Store</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .offline-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem;
            text-align: center;
        }
        
        .offline-icon {
            font-size: 5rem;
            color: var(--color-primary);
            margin-bottom: 2rem;
        }
        
        .offline-title {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            margin-bottom: 2rem;
            max-width: 600px;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            <i class="fas fa-wifi"></i>
        </div>
        <h1 class="offline-title">Sei offline</h1>
        <p class="offline-message">
            Non sei connesso a Internet. Alcune funzionalità potrebbero non essere disponibili.
            Puoi comunque navigare tra i servizi e compilare il form di prenotazione.
            Le prenotazioni saranno salvate localmente e sincronizzate quando tornerai online.
        </p>
        <button class="btn btn-primary" onclick="window.location.reload()">Riprova</button>
    </div>
    
    <script>
        // Controlla periodicamente se la connessione è tornata
        setInterval(() => {
            if (navigator.onLine) {
                window.location.reload();
            }
        }, 5000);
    </script>
</body>
</html>
