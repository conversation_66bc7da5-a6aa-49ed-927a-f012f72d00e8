The id for DASHBOARD is tblAanKl9ORXkyFEr. Table ids and table names can be used interchangeably in API requests. Using table ids means table name changes do not require modifications to your API request.

Fields
Each record in the DASHBOARD table contains the following fields:

Field names and field ids can be used interchangeably. Using field ids means field name changes do not require modifications to your API request. We recommend using field ids over field names where possible, to reduce modifications to your API request if the user changes the field name later.

Field NameField IDTypeDescription
IDfld7MXZlNXEU0KJeBFormula
number, string, array of numbers or strings
Computed value: Nome.
 
Example values
"\"Nome Cognome - xxx\""

"\"Nome Cognome - xxx\""

"\"Teresa Scotto Di <PERSON>lo - 09/01/1977\""

"\"Susy  Barone - xxx\""

"\"Lisa  Pugliese - 03/03/1993\""

NomefldeQB6GY0PFS5nKE
Link to another record
array of record IDs (strings)
Array of linked records IDs from the Anagrafica Clienti table.
 
Example value
["rec8116cdd76088af", "rec245db9343f55e8", "rec4f3bade67ff565"]

TRATTAMENTO (from Nome)fldUVNlAQoaMushe1
Lookup
array of numbers, strings, booleans, or objects
Array of TRATTAMENTO fields in linked Anagrafica Clienti records.
 
Example values
[
    "TRATTAMENTO"
]

[
    "TRATTAMENTO"
]

[
    "Presso e massaggio"
]

[
    "Pressoterapia e massaggio"
]

[
    null
]

Coscia Prossimale MISURE (from Nome)fldKtMFMMr7QntW8p
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia Prossimale MISURE fields in linked Anagrafica Clienti records.
 
Example values
[
    58
]

[
    74
]

Coscia Prossimale MISURE 2 (from Nome)fldE6ms4Ct4AGmFdg
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia Prossimale MISURE 2 fields in linked Anagrafica Clienti records.
 
Example values
[
    2
]

[
    2
]

[
    76
]

[
    2
]

Coscia Prossimale MISURE 3 (from Nome)fld0ns4Vqa47KANlB
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia Prossimale MISURE 3 fields in linked Anagrafica Clienti records.
 
Example values
[
    3
]

[
    3
]

[
    3
]

Coscia Distale MISURE (from Nome)fldIstfSQPcEICJao
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia Distale MISURE fields in linked Anagrafica Clienti records.
 
Example values
[
    41
]

[
    56
]

Coscia Distale MISURE 2 (from Nome)fldolI70OsYwf18RY
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia Distale MISURE 2 fields in linked Anagrafica Clienti records.
 
Example values
[
    2
]

[
    2
]

[
    58
]

[
    2
]

Coscia Distale MISURE 3 (from Nome)fld6F5Ji76OIBMUc6
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia Distale MISURE 3 fields in linked Anagrafica Clienti records.
 
Example values
[
    3
]

[
    3
]

[
    3
]

DATA SEDUTA 08 (from Nome) 2fld4J2nKGff4w2SIV
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 08 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-08-01"
]

[
    "2001-08-01"
]

[
    "2001-08-01"
]

DATA SEDUTA 09 (from Nome)fldUKkzw3pdQCpEPM
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 09 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-09-01"
]

[
    "2001-09-01"
]

[
    "2001-09-01"
]

DATA SEDUTA 10 (from Nome)fld66ozN6EIelvjrx
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 10 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-10-01"
]

[
    "2001-10-01"
]

[
    "2001-10-01"
]

AVANZAMENTO SEDUTA 01 (from Nome)fld2o9NhJHFHdBaF2
Lookup
array of numbers, strings, booleans, or objects
Array of AVANZAMENTO SEDUTA 01 fields in linked Anagrafica Clienti records.
 
Example values
To see example values, try adding some data to DASHBOARD.

AVANZAMENTO SEDUTA 02 (from Nome)fldZi9qpVYFkNkFtu
Lookup
array of numbers, strings, booleans, or objects
Array of AVANZAMENTO SEDUTA 02 fields in linked Anagrafica Clienti records.
 
Example values
To see example values, try adding some data to DASHBOARD.

AVANZAMENTO SEDUTA 03 (from Nome)fldAo48QMklTa27WH
Lookup
array of numbers, strings, booleans, or objects
Array of AVANZAMENTO SEDUTA 03 fields in linked Anagrafica Clienti records.
 
Example values
To see example values, try adding some data to DASHBOARD.

DATA SEDUTA 01 (from Nome)flduUtp56DB8LBxpF
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 01 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-01-01"
]

[
    "2001-01-01"
]

[
    "2001-01-01"
]

DATA SEDUTA 02 (from Nome)fldE3BSvH2j1CPItu
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 02 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-02-01"
]

[
    "2001-02-01"
]

[
    "2001-02-01"
]

DATA SEDUTA 03 (from Nome)fldaH5WmgnfVEkPwD
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 03 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-03-01"
]

[
    "2001-03-01"
]

[
    "2001-03-01"
]

DATA SEDUTA 04 (from Nome)fldq7S1VbaYcz8VZl
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 04 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-04-01"
]

[
    "2001-04-01"
]

[
    "2001-04-01"
]

DATA SEDUTA 05 (from Nome)fldGTCTyV3ji2pUJR
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 05 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-05-01"
]

[
    "2001-05-01"
]

[
    "2001-05-01"
]

DATA SEDUTA 06 (from Nome)fld3UsSxtpR4CDYm8
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 06 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-06-01"
]

[
    "2001-06-01"
]

[
    "2001-06-01"
]

DATA SEDUTA 07 (from Nome)fldSYjIgDoH4IL1A4
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 07 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-07-01"
]

[
    "2001-07-01"
]

[
    "2001-07-01"
]

DATA SEDUTA 08 (from Nome)fldckCTINvOiLsHGK
Lookup
array of numbers, strings, booleans, or objects
Array of DATA SEDUTA 08 fields in linked Anagrafica Clienti records.
 
Example values
[
    "2001-08-01"
]

[
    "2001-08-01"
]

[
    "2001-08-01"
]

Peso MISURE (from Nome) 2fldmVG9FT7QSPzNoJ
Lookup
array of numbers, strings, booleans, or objects
Array of Peso MISURE fields in linked Anagrafica Clienti records.
 
Example values
[
    57
]

[
    118.5
]

Peso MISURE 2 (from Nome)fldW4MwCItiwGGvAR
Lookup
array of numbers, strings, booleans, or objects
Array of Peso MISURE 2 fields in linked Anagrafica Clienti records.
 
Example values
[
    2
]

[
    2
]

[
    109
]

[
    2
]

Peso MISURE 3 (from Nome)fld1PAiPIYHpDINlj
Lookup
array of numbers, strings, booleans, or objects
Array of Peso MISURE 3 fields in linked Anagrafica Clienti records.
 
Example values
[
    3
]

[
    3
]

[
    3
]

Braccia MISURE (from Nome) 2fldQEFRb1PnzAIQYq
Lookup
array of numbers, strings, booleans, or objects
Array of Braccia MISURE fields in linked Anagrafica Clienti records.
 
Example values
To see example values, try adding some data to DASHBOARD.

Braccia MISURE 2 (from Nome)fld58Ba4OCWMVBQH2
Lookup
array of numbers, strings, booleans, or objects
Array of Braccia MISURE 2 fields in linked Anagrafica Clienti records.
 
Example values
[
    2
]

[
    2
]

[
    2
]

Braccia MISURE 3 (from Nome)fld8x6Ta6c7fMGyAb
Lookup
array of numbers, strings, booleans, or objects
Array of Braccia MISURE 3 fields in linked Anagrafica Clienti records.
 
Example values
[
    3
]

[
    3
]

[
    3
]

Vite MISURE (from Nome) 2fldcUD3HbUluhlSV3
Lookup
array of numbers, strings, booleans, or objects
Array of Vite MISURE fields in linked Anagrafica Clienti records.
 
Example values
[
    85
]

[
    133
]

Vite MISURE 2 (from Nome)fld8Phak5hOxwSgB9
Lookup
array of numbers, strings, booleans, or objects
Array of Vite MISURE 2 fields in linked Anagrafica Clienti records.
 
Example values
[
    2
]

[
    2
]

[
    135
]

[
    2
]

Vite MISURE 3 (from Nome)fld4wAdcmlQnwkEID
Lookup
array of numbers, strings, booleans, or objects
Array of Vite MISURE 3 fields in linked Anagrafica Clienti records.
 
Example values
[
    3
]

[
    3
]

[
    3
]

Coscia MISURE (from Nome) 2fldANcQ1lEL9IfB47
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia MISURE fields in linked Anagrafica Clienti records.
 
Example values
To see example values, try adding some data to DASHBOARD.

Coscia MISURE 2 (from Nome)fldAkDFmjskM6chjs
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia MISURE 2 fields in linked Anagrafica Clienti records.
 
Example values
[
    2
]

[
    2
]

[
    2
]

Coscia MISURE 3 (from Nome)fld5tAs8N2LPJGPjX
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia MISURE 3 fields in linked Anagrafica Clienti records.
 
Example values
[
    3
]

[
    3
]

[
    3
]

Caviglia MISURE (from Nome) 2flder2eXHsInnW6uz
Lookup
array of numbers, strings, booleans, or objects
Array of Caviglia MISURE fields in linked Anagrafica Clienti records.
 
Example values
[
    21
]

[
    27
]

Caviglia MISURE 2 (from Nome)fldfuYuw78unjbF0T
Lookup
array of numbers, strings, booleans, or objects
Array of Caviglia MISURE 2 fields in linked Anagrafica Clienti records.
 
Example values
[
    2
]

[
    2
]

[
    26
]

[
    2
]

Caviglia MISURE 3 (from Nome)fldF9REA8LDwgYxEf
Lookup
array of numbers, strings, booleans, or objects
Array of Caviglia MISURE 3 fields in linked Anagrafica Clienti records.
 
Example values
[
    3
]

[
    3
]

[
    3
]

TIPO APPUNTAMENTOfldjC3PeOdhxhs2lb
Single select
string
Selected option name.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[
    "STANDARD",
    "TRATTAMENTO",
    "+",
    "**"
]
INFO SEDUTAfldlBrvOaiqp1DBBPFormula
number, string, array of numbers or strings
Computed value: CONCATENATE({TIPO APPUNTAMENTO},"-",{N° SEDUTA}).
 
Example values
"\"STANDARD-SEDUTA 01\""

"\"STANDARD-\""

"\"STANDARD-\""

"\"STANDARD-SEDUTA 01\""

"\"STANDARD-\""

TRATTAMENTOfldToKv6NpWAUF0er
Single select
string
Selected option name.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[
    "RENATA FRANCA",
    "Acidi",
    "PRESSOTERAPIA + MASSAGGI",
    "BENDAGGI",
    "MASSAGGI",
    "RADIOFREQUENZA",
    "ENDOSPHERES",
    "LASER",
    "LASER MANTENIMENTO",
    "MASSAGGI VISO",
    "cera braccia"
]
Telefono (from Nome)fld7LEQ8alu41q16x
Lookup
array of numbers, strings, booleans, or objects
Array of Telefono fields in linked Anagrafica Clienti records.
 
Example values
[
    "Telefono"
]

[
    "Telefono"
]

[
    "393208558334"
]

[
    "393318344358"
]

[
    "393298129961"
]

Email (from Nome)fldSHd2l1H6rmFnme
Lookup
array of numbers, strings, booleans, or objects
Array of Email fields in linked Anagrafica Clienti records.
 
Example values
[
    "Email"
]

[
    "Email"
]

[
    "<EMAIL>"
]

[
    "<EMAIL>"
]

[
    "<EMAIL>"
]

Data di nascita (from Nome)fldvjxTwCJOwKv18w
Lookup
array of numbers, strings, booleans, or objects
Array of Data di nascita fields in linked Anagrafica Clienti records.
 
Example values
[
    "1977-01-09"
]

[
    "1993-03-03"
]

[
    "2025-04-11"
]

[
    "2025-04-11"
]

Nato a (from Nome)fldybYQUt4RctM5eB
Lookup
array of numbers, strings, booleans, or objects
Array of Nato a fields in linked Anagrafica Clienti records.
 
Example values
[
    "Note CORPO 3"
]

[
    "Note CORPO 3"
]

[
    "Note CORPO 3"
]

Età (from Nome)fldv264TmMD8fRXYr
Lookup
array of numbers, strings, booleans, or objects
Array of Età fields in linked Anagrafica Clienti records.
 
Example values
[
    {
        "specialValue": "NaN"
    }
]

[
    {
        "specialValue": "NaN"
    }
]

[
    48
]

[
    {
        "specialValue": "NaN"
    }
]

[
    32
]

Professione (from Nome)fldqeCqvdy991zc8V
Lookup
array of numbers, strings, booleans, or objects
Array of Professione fields in linked Anagrafica Clienti records.
 
Example values
[
    "Professione"
]

[
    "Professione"
]

[
    "Professione"
]

Datafldov4MpjUqqn9o8nFormula
number, string, array of numbers or strings
Computed value: IF({Data Appuntamento}="",DATETIME_FORMAT({Data Appuntamento},'DD/MM/YY'),DATETIME_FORMAT({Data Appuntamento},'DD/MM/YY')).
 
Example values
"\"15/04/25\""

"\"08/04/25\""

"\"08/04/25\""

"\"09/04/25\""

"\"15/04/25\""

Ora AppuntamentofldQy0QZOGFMquGOaFormula
number, string, array of numbers or strings
Computed value: IF({Data Appuntamento}="",DATETIME_FORMAT({Data Appuntamento},'HH:mm'),DATETIME_FORMAT({Data Appuntamento},'HH:mm')).
 
Example values
"\"01:30\""

"\"12:08\""

"\"12:22\""

"\"15:48\""

"\"03:30\""

Data AppuntamentofldiqkqE7Tomkd8Gk
Date and time
string (ISO 8601 formatted date)
UTC date and time, e.g. "2014-09-05T07:00:00.000Z".
 
Example values
"2025-04-15T01:30:00.000Z"

"2025-04-08T12:08:00.000Z"

"2025-04-08T12:22:00.000Z"

"2025-04-09T15:48:00.000Z"

"2025-04-15T03:30:00.000Z"

Durata TOTALE Appuntamentofld0bYKpePunsZQhkFormula
number, string, array of numbers or strings
Computed value: IF(Operatrici="Francesca Cotugno",SUM({Durata-Francesca (from Servizi)}),IF(Operatrici="Viviana Guardascione",SUM({Durata-Viviana (from Servizi)}),IF(Operatrici="Viola Basile",SUM({Durata-Viola (from Servizi)}),IF(Operatrici="Sara Sangiovanni",SUM({Durata-Sara (from Servizi)}),{Durata Appuntamento})))).
 
Example values
3600

300

900

300

300

Orario fine appuntamentofldlWzcJtqhDYIGVSFormula
number, string, array of numbers or strings
Computed value: DATEADD({Data Appuntamento}, {Durata TOTALE Appuntamento}, 'seconds').
 
Example values
"2025-04-15T02:30:00.000Z"

"2025-04-08T12:13:00.000Z"

"2025-04-08T12:37:00.000Z"

"2025-04-09T15:53:00.000Z"

"2025-04-15T03:35:00.000Z"

ServizifldGwV8NxVAyd8s3g
Link to another record
array of record IDs (strings)
Array of linked records IDs from the Servizi table.
 
Example value
["rec8116cdd76088af", "rec245db9343f55e8", "rec4f3bade67ff565"]

SERVIZI DA APPfldR8Em6XAxSGeJhp
Multiple select
array of strings
Array of selected option names.

When creating or updating records, if a choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[
    "Baffetto",
    "Baffetto e sopracciglia",
    "Bendaggi",
    "Bendaggi + massaggio 30 min.",
    "Cera ascelle",
    "Cera braccia",
    "Cera completa",
    "Cera coscia",
    "Cera gambaletto",
    "Cera inguine",
    "Cera inguine integrale",
    "Cera pancia",
    "Cera schiena",
    "Cera uomo addome/schiena",
    "Copertura in gel",
    "Endospheres",
    "Henné sopracciglia",
    "Laminazione ciglia",
    "Laminazione sopracciglia",
    "Laser addome completo",
    "Laser addome zona Alba",
    "Laser ascelle",
    "Laser baffetto",
    "Laser basette",
    "Laser braccia",
    "Laser coscia",
    "Laser gambaletto",
    "Laser gambe",
    "Laser inguine",
    "Laser mento",
    "Laser schiena completa",
    "Laser schiena parte bassa",
    "Laser viso completo",
    "Manicure",
    "Massaggio con metodo Renata Franca",
    "Massaggio decontratturante",
    "Massaggio linfodrenante 30 min.",
    "Massaggio linfodrenante 50 min.",
    "Massaggio relax",
    "Massaggio viso",
    "Pedicure combi",
    "Pedicure con semipermanente",
    "Pedicure con trattamento callosità",
    "Pedicure curativo",
    "Pedicure estetico",
    "Peeling viso",
    "Pressoterapia",
    "Pressoterapia + massaggio 30 min.",
    "Pressoterapia + massaggio  50 min.",
    "Pulizia del viso classica",
    "Pulizia del viso con macchinari",
    "Radiofrequenza viso",
    "Radiofrequenza addome",
    "cosce",
    "glutei",
    "Ricostruzione",
    "Scrub braccia",
    "Scrub corpo",
    "Scrub gambe",
    "Semipermanente clássico",
    "Semipermanente combi",
    "Semipermanente piedi",
    "Sopracciglia",
    "Trattamento argilla viso",
    "Trattamento bava di lumaca",
    "Trattamento spirulina viso",
    "Trattamento viso base + massaggio"
]
Prezzo totalefld4EQ2KhLMlG2gDSFormula
number, string, array of numbers or strings
Computed value: SUM({Prezzo (from Servizi)}).
 
Example values
35

3

5

3

3

OperatriciflduF2dscJGmDv2CG
Single select
string
Selected option name.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[
    "Francesca Cotugno",
    "Mena Sardo",
    "Viola Basile",
    "Marcella Illiano",
    "Elena Beauty",
    "Marzia Fascelli",
    "Giorgia Lucignano"
]
Beauty TablefldcKpDLAw6H2KfDg
Single select
string
Selected option name.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[
    "01 - ARTEMIDE",
    "02 - APHRODITE",
    "03 - ATENA"
]
Beauty RoomfldL2eo8k0w66MlnI
Single select
string
Selected option name.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[
    "01 - AURA",
    "02 - DEMETRA",
    "03 - CIRCE",
    "04 - ESTIA",
    "05 - IRIS"
]
STATOfldLrPTPOxSFZR7fQ
Single select
string
Selected option name.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[
    "CONFERMATO",
    "CANCELLATO",
    "IN ATTESA",
    "RICHIESTO"
]
WHATSAPPfldGKPKWpnpaI7ngk
Button
button object
Object providing details about the button configuration.

labelstring
button label
urlstring
for "Open URL" actions, the computed url value
 
Example value
{
    "label": "WHATSAPP",
    "url": "https://wa.me/Telefono/?text=Salve, sono Merilù di Aphrodite, le volevo confermare l’appuntamento di domani, Baffetto e sopracciglia , Bendaggi alle ore 01:30, buona giornata."
}

TRATTAMENTI DA EFFETTUAREfld9fVLkzqZDXpDh5
Long text (with rich text formatting enabled)
string
A Markdown-inspired markup language.
Learn more about using Markdown in long text's rich text formatting API.
 
Example value
[
    {
        "type": "paragraph",
        "value": [
            {
                "type": "paragraphLine",
                "value": [
                    {
                        "type": "text",
                        "value": "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam et nulla vulputate, sollicitudin risus sed, suscipit sapien. "
                    },
                    {
                        "type": "text",
                        "value": "Donec ac tempus velit, nec efficitur augue. Donec fermentum ex cursus, tincidunt justo ac, bibendum turpis",
                        "attributes": {
                            "bold": true,
                        }
                    }
                ]
            },
            {
                "type": "paragraphLine",
                "value": [
                    {
                        "type": "mention",
                        "value": {
                            "userName": "Waffles Wags-a-lot",
                            "userId": "usrR2o0pNsHC4FdrT",
                            "mentionId": "menE1i9oBaGX3DseR"
                        },
                        "attributes": {
                            "bold": true
                        }
                    }
                ]
            }
        ]
    }
]
                            

NUMERO SEDUTA TRATTAMENTOflduRDniRHTlKHTG5
Single select
string
Selected option name.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[
    "SEDUTA NUMERO 01",
    "SEDUTA NUMERO 02",
    "SEDUTA NUMERO 03",
    "SEDUTA NUMERO 04",
    "SEDUTA NUMERO 05",
    "SEDUTA NUMERO 06",
    "SEDUTA NUMERO 07",
    "SEDUTA NUMERO 08",
    "SEDUTA NUMERO 09",
    "SEDUTA NUMERO 10",
    "SEDUTA NUMERO 11",
    "SEDUTA NUMERO 12",
    "SEDUTA NUMERO 13",
    "SEDUTA NUMERO 14",
    "SEDUTA NUMERO 15"
]
N° SEDUTAfldsUkpsn3rsk9uj7
Single select
string
Selected option name.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[
    "SEDUTA 01",
    "SEDUTA 02",
    "SEDUTA 03",
    "SEDUTA 04",
    "SEDUTA 05",
    "SEDUTA 06",
    "SEDUTA 07",
    "SEDUTA 08",
    "SEDUTA 09",
    "SEDUTA 10",
    "SEDUTA 11",
    "SEDUTA 12",
    "SEDUTA 13",
    "SEDUTA 14",
    "SEDUTA 15"
]
NOME DA APPfldA5bGAuec833u9HText
string
A single line of text.
 
Example values
"foo", "bar"

COGNOME DA APPfldIr1J82S7JsjHvUText
string
A single line of text.
 
Example values
"foo", "bar"

CreatedfldOuGI9DbNU7ieWFCreated time
string
The time the record was created in UTC, e.g. "2015-08-29T07:00:00.000Z".
 
Example values
"2025-04-13T09:58:41.000Z"

"2025-04-13T10:09:04.000Z"

"2025-04-13T10:22:51.000Z"

"2025-04-28T13:48:25.000Z"

"2025-04-28T13:52:03.000Z"

Ora FinefldTh527502SP6RwlFormula
number, string, array of numbers or strings
Computed value: IF({Orario fine appuntamento}="",DATETIME_FORMAT({Data Appuntamento},'HH:mm'),DATETIME_FORMAT({Orario fine appuntamento},'HH:mm')).
 
Example values
"\"02:30\""

"\"12:13\""

"\"12:37\""

"\"15:53\""

"\"03:35\""

DATA COMPLETAfldhN7aWPZQYFjTLPFormula
number, string, array of numbers or strings
Computed value: CONCATENATE(Data," ",{Ora Appuntamento}).
 
Example values
"\"15/04/25 01:30\""

"\"08/04/25 12:08\""

"\"08/04/25 12:22\""

"\"09/04/25 15:48\""

"\"15/04/25 03:30\""

Nome (from Nome)fldPIUli905CG4kUE
Lookup
array of numbers, strings, booleans, or objects
Array of Nome fields in linked Anagrafica Clienti records.
 
Example values
[
    "Nome"
]

[
    "Nome"
]

[
    "Teresa"
]

[
    "Susy "
]

[
    "Lisa "
]

Cognome (from Nome)fldeEc2uLDmKzenNP
Lookup
array of numbers, strings, booleans, or objects
Array of Cognome fields in linked Anagrafica Clienti records.
 
Example values
[
    "Cognome"
]

[
    "Cognome"
]

[
    "Scotto Di Santolo"
]

[
    "Barone"
]

[
    "Pugliese"
]

Note01 (from Nome)fldu9Q7E9C9ITxBOr
Lookup
array of numbers, strings, booleans, or objects
Array of Note01 fields in linked Anagrafica Clienti records.
 
Example values
[
    "Note01"
]

[
    "Note01"
]

[
    "Si. pressoterapia e massaggio"
]

[
    "Note01"
]

Creme usate (from Nome)fldhzDzhpThpVAyON
Lookup
array of numbers, strings, booleans, or objects
Array of Creme usate fields in linked Anagrafica Clienti records.
 
Example values
[
    "Creme usate"
]

[
    "Creme usate"
]

[
    "Olio oli essenziali "
]

[
    "Olio di mandorle "
]

[
    null
]

Allergie (from Nome)fld2a08XILM3n4pDv
Lookup
array of numbers, strings, booleans, or objects
Array of Allergie fields in linked Anagrafica Clienti records.
 
Example values
[
    "Allergie"
]

[
    "Allergie"
]

[
    "No"
]

[
    "No"
]

[
    "polline"
]

Cure in corso (from Nome)fldh5M9CjmVMHtRmJ
Lookup
array of numbers, strings, booleans, or objects
Array of Cure in corso fields in linked Anagrafica Clienti records.
 
Example values
[
    "Cure in corso"
]

[
    "Cure in corso"
]

[
    "No"
]

[
    null
]

[
    null
]

Ciclo metruale (from Nome)fldByLZxQj7yZecdj
Lookup
array of numbers, strings, booleans, or objects
Array of Ciclo metruale fields in linked Anagrafica Clienti records.
 
Example values
[
    "Ciclo metruale"
]

[
    "Ciclo metruale"
]

[
    "Regolare"
]

[
    "Menopausa"
]

[
    "Ciclo metruale"
]

Stile di vita (from Nome)fldi7I9YAOYuPAxrc
Lookup
array of numbers, strings, booleans, or objects
Array of Stile di vita fields in linked Anagrafica Clienti records.
 
Example values
[
    "Stile di vita"
]

[
    "Stile di vita"
]

[
    "Sedentaria "
]

[
    "Frenetico"
]

[
    "Stile di vita"
]

Note CORPO (from Nome)fldK8LvXz2zfEg1Pn
Lookup
array of numbers, strings, booleans, or objects
Array of Note CORPO fields in linked Anagrafica Clienti records.
 
Example values
[
    "Note CORPO"
]

[
    "Note CORPO"
]

[
    null
]

[
    null
]

[
    "Note CORPO"
]

Peso MISURE (from Nome)fldKgrYXs2ZG6cNg0
Lookup
array of numbers, strings, booleans, or objects
Array of Peso MISURE fields in linked Anagrafica Clienti records.
 
Example values
[
    57
]

[
    118.5
]

Braccia MISURE (from Nome)fldSe6vGfOC2ZRzmX
Lookup
array of numbers, strings, booleans, or objects
Array of Braccia MISURE fields in linked Anagrafica Clienti records.
 
Example values
To see example values, try adding some data to DASHBOARD.

Vite MISURE (from Nome)fldoyWfYGxkg49z9A
Lookup
array of numbers, strings, booleans, or objects
Array of Vite MISURE fields in linked Anagrafica Clienti records.
 
Example values
[
    85
]

[
    133
]

Coscia MISURE (from Nome)fldLBT1pphYfrw7a4
Lookup
array of numbers, strings, booleans, or objects
Array of Coscia MISURE fields in linked Anagrafica Clienti records.
 
Example values
To see example values, try adding some data to DASHBOARD.

Caviglia MISURE (from Nome)fldCR4I0jeAPlpxzV
Lookup
array of numbers, strings, booleans, or objects
Array of Caviglia MISURE fields in linked Anagrafica Clienti records.
 
Example values
[
    21
]

[
    27
]

Note VISO (from Nome)fldwcFFIzwHuLaIDJ
Lookup
array of numbers, strings, booleans, or objects
Array of Note VISO fields in linked Anagrafica Clienti records.
 
Example values
[
    "Note VISO"
]

[
    "Note VISO"
]

[
    null
]

[
    null
]

[
    "Note VISO"
]

Pelle (from Nome)fldfDzpm43BFM0nV6
Lookup
array of numbers, strings, booleans, or objects
Array of Pelle fields in linked Anagrafica Clienti records.
 
Example values
[
    "Pelle"
]

[
    "Pelle"
]

[
    "Pelle"
]

Prezzo (from Servizi)fldZYdxsQptH85LuF
Lookup
array of numbers, strings, booleans, or objects
Array of Prezzo fields in linked Servizi records.
 
Example values
[
    5,
    30
]

[
    3
]

[
    5
]

[
    3
]

[
    3
]

Durata-Francesca (from Servizi)fldmTuCL0QrH5AYr5
Lookup
array of numbers, strings, booleans, or objects
Array of Durata-Francesca fields in linked Servizi records.
 
Example values
[
    900,
    2700
]

[
    300
]

[
    900
]

[
    300
]

[
    300
]

Durata-Viviana (from Servizi)fldUwRQbIckZnisF2
Lookup
array of numbers, strings, booleans, or objects
Array of Durata-Viviana fields in linked Servizi records.
 
Example values
[
    900,
    2700
]

[
    300
]

[
    900
]

[
    300
]

[
    300
]

Durata-Viola (from Servizi)fldnhqtzR20OHmuAX
Lookup
array of numbers, strings, booleans, or objects
Array of Durata-Viola fields in linked Servizi records.
 
Example values
[
    900,
    2700
]

[
    300
]

[
    900
]

[
    300
]

[
    300
]

Durata-Sara (from Servizi)fldo6y5k25Pn5rkbp
Lookup
array of numbers, strings, booleans, or objects
Array of Durata-Sara fields in linked Servizi records.
 
Example values
[
    900,
    2700
]

[
    300
]

[
    900
]

[
    300
]

[
    300
]

Durata (from Servizi)fldcL0HAd359AuK9c
Lookup
array of numbers, strings, booleans, or objects
Array of Durata fields in linked Servizi records.
 
Example values
[
    900,
    2700
]

[
    300
]

[
    900
]

[
    300
]

[
    300
]

Durata Appuntamentofldvxei4tIgqo58xrFormula
number, string, array of numbers or strings
Computed value: SUM({Durata (from Servizi)}).
 
Example values
3600

300

900

300

300

Servizi (from Servizi)fld5ybL3zHnB1NlT1
Lookup
array of numbers, strings, booleans, or objects
Array of Servizi fields in linked Servizi records.
 
Example values
[
    "Baffetto e sopracciglia",
    "Bendaggi"
]

[
    "Baffetto"
]

[
    "Baffetto e sopracciglia"
]

[
    "Baffetto"
]

[
    "Baffetto"
]

CONTROLLOfldq7stRmmDiydiqJFormula
number, string, array of numbers or strings
Computed value: IF(STATO="CONFERMATO",{Servizi (from Servizi)}={SERVIZI DA APP},"").
 
Example values
To see example values, try adding some data to DASHBOARD.

VIEWfldBZW6kETyVQOZam
Single select
string
Selected option name.

Empty, because there are no select choices.

When creating or updating records, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error if a choice is supplied unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created.

When creating or updating records, if the choice string does not exactly match an existing option, the request will fail with an INVALID_MULTIPLE_CHOICE_OPTIONS error unless the typecast parameter is enabled. If typecast is enabled, a new choice will be created if one does not exactly match.

 
Possible values
[]
PRENOTAZIONIfldY0z3UqSHHrkzWHText
string
A single line of text.
 
Example values
"foo", "bar"

STAMPA SCHEDAfld1sBaklJ64Rvcni
Button
button object
Object providing details about the button configuration.

labelstring
button label
urlstring
for "Open URL" actions, the computed url value
 
Example value
{
    "label": "SCHEDA INIZIALE",
    "url": "https://airtable.com/tblAanKl9ORXkyFEr/rec9skTKn0vVvNQv7?blocks=bliU6O8XD3UVAZTRx"
}

Open URLfldajAmr5pZP4lCSu
Button
button object
Object providing details about the button configuration.

labelstring
button label
urlstring
for "Open URL" actions, the computed url value
 
Example value
{
    "label": "STAMPA",
    "url": "https://airtable.com/appbA9iuzZXzYWHI6/tblAanKl9ORXkyFEr/viwHviEyrRrhDydzv/rec9skTKn0vVvNQv7?blocks=hide"
}

Field 75fldxIxWC69guFsRsjText
string
A single line of text.
 
Example values
"foo", "bar"

Field 76fldnucoBQtpnmSYvnText
string
A single line of text.
 
Example values
"foo", "bar"

List DASHBOARD records
To list records in DASHBOARD, use the select method.

select returns a query object. To fetch the records matching that query, use the eachPage or firstPage method of the query object.

Returned records do not include any fields with "empty" values, e.g. "", [], or false.

Note: Airtable's API only accepts request with a URL shorter than 16,000 characters. Encoded formulas may cause your requests to exceed this limit. To fix this issue you can instead make a POST request to /v0/{baseId}/{tableIdOrName}/listRecords while passing the parameters within the body of the request instead of the query parameters. See our support article on this for more information.

You can use the following parameters to filter, sort, and format the results:

fields
array of strings
optional
Only data for fields whose names are in this list will be included in the result. If you don't need every field, you can use this parameter to reduce the amount of data transferred.

For example, to only return data from ID and Nome, pass in:

fields: ["ID", "Nome"]
You can also perform the same action with field ids (they can be found in the fields section):

fields: ["fld7MXZlNXEU0KJeB", "fldeQB6GY0PFS5nKE"]
filterByFormula
string
optional
A formula used to filter records. The formula will be evaluated for each record, and if the result is not 0, false, "", NaN, [], or #Error! the record will be included in the response. We recommend testing your formula in the Formula field UI before using it in your API request.

If combined with the view parameter, only records in that view which satisfy the formula will be returned.

The formula must be encoded first before passing it as a value. You can use this tool to not only encode the formula but also create the entire url you need. For example, to only include records where ID isn't empty, pass in NOT({ID} = '') as a parameter like this:

filterByFormula: "NOT({ID} = '')"

Note: Airtable's API only accepts request with a URL shorter than 16,000 characters. Encoded formulas may cause your requests to exceed this limit. To fix this issue you can instead make a POST request to /v0/{baseId}/{tableIdOrName}/listRecords while passing the parameters within the body of the request instead of the query parameters. See our support article on this for more information.

maxRecords
number
optional
The maximum total number of records that will be returned in your requests. If this value is larger than pageSize (which is 100 by default), you may have to load multiple pages to reach this total. See the Pagination section below for more.pageSize
number
optional
The number of records returned in each request. Must be less than or equal to 100. Default is 100. See the Pagination section below for more.sort
array of objects
optional
A list of sort objects that specifies how the records will be ordered. Each sort object must have a field key specifying the name of the field to sort on, and an optional direction key that is either "asc" or "desc". The default direction is "asc".

The sort parameter overrides the sorting of the view specified in the view parameter. If neither the sort nor the view parameter is included, the order of records is arbitrary.

For example, to sort records by ID in descending order, send these two query parameters:

sort%5B0%5D%5Bfield%5D=ID
sort%5B0%5D%5Bdirection%5D=desc
For example, to sort records by ID in descending order, pass in:

[{field: "ID", direction: "desc"}]
view
string
optional
The name or ID of a view in the DASHBOARD table. If set, only the records in that view will be returned. The records will be sorted according to the order of the view unless the sort parameter is included, which overrides that order. Fields hidden in this view will be returned in the results. To only return a subset of fields, use the fields parameter.cellFormat
string
optional
The format that should be used for cell values. Supported values are:

json: cells will be formatted as JSON, depending on the field type.

string: cells will be formatted as user-facing strings, regardless of the field type. The timeZone and userLocale parameters are required when using string as the cellFormat.

Note: You should not rely on the format of these strings, as it is subject to change.
The default is json.

timeZone
string
optional
The time zone that should be used to format dates when using string as the cellFormat. This parameter is required when using string as the cellFormat.

userLocale
string
optional
The user locale that should be used to format dates when using string as the cellFormat. This parameter is required when using string as the cellFormat.

returnFieldsByFieldId
boolean
optional
An optional boolean value that lets you return field objects where the key is the field id.

This defaults to false, which returns field objects where the key is the field name.

recordMetadata
array of strings
optional
An optional field that, if includes commentCount, adds a commentCount read only property on each record returned.

Pagination
The server returns one page of records at a time. Each page will contain pageSize records, which is 100 by default.

To fetch the next page of records, call fetchNextPage.

Pagination will stop when you've reached the end of your table. If the maxRecords parameter is passed, pagination will stop once you've reached this maximum.

 
Code
var Airtable = require('airtable');
var base = new Airtable({apiKey: 'YOUR_SECRET_API_TOKEN'}).base('appbZ4ONNnQp69VtM');

base('DASHBOARD').select({
    // Selecting the first 3 records in RICHIESTA PRENOTAZIONE:
    maxRecords: 3,
    view: "RICHIESTA PRENOTAZIONE"
}).eachPage(function page(records, fetchNextPage) {
    // This function (`page`) will get called for each page of records.

    records.forEach(function(record) {
        console.log('Retrieved', record.get('ID'));
    });

    // To fetch the next page of records, call `fetchNextPage`.
    // If there are more records, `page` will get called again.
    // If there are no more records, `done` will get called.
    fetchNextPage();

}, function done(err) {
    if (err) { console.error(err); return; }
});
Output
Retrieved Nome Cognome - xxx
Retrieved Nome Cognome - xxx
Retrieved Teresa Scotto Di Santolo - 09/01/1977
Fetch first page
// If you only want the first page of records, you can
// use `firstPage` instead of `eachPage`.
base('DASHBOARD').select({
    view: 'RICHIESTA PRENOTAZIONE'
}).firstPage(function(err, records) {
    if (err) { console.error(err); return; }
    records.forEach(function(record) {
        console.log('Retrieved', record.get('ID'));
    });
});
Fetch additional record metadata
// If you want to fetch the number of comments for each record,
// include the `recordMetadata` param.
base('DASHBOARD').select({
    recordMetadata: ['commentCount']
}).firstPage(function(err, records) {
    if (err) { console.error(err); return; }
    records.forEach(function(record) {
        console.log('Retrieved a record with', record.commentCount, 'comments');
    });
});
 
Iteration may timeout due to client inactivity or server restarts. In that case, the client will receive a 422 response with error message LIST_RECORDS_ITERATOR_NOT_AVAILABLE. It may then restart iteration from the beginning.