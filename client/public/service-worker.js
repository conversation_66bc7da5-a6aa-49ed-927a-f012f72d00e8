// Service Worker for Aphrodite Center Store PWA
const CACHE_NAME = 'aphrodite-pwa-v1';
const DYNAMIC_CACHE = 'aphrodite-dynamic-v1';

// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.svg',
  '/offline.html',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
];

// Install event - Cache static assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing...');
  
  // Skip waiting to ensure the new service worker activates immediately
  self.skipWaiting();
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[Service Worker] Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
  );
});

// Activate event - Clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating...');
  
  // Claim clients to ensure the SW controls all clients immediately
  event.waitUntil(self.clients.claim());
  
  // Clean up old caches
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== DYNAMIC_CACHE) {
            console.log('[Service Worker] Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Helper function to determine if a request is an API request
function isApiRequest(url) {
  return url.includes('/api/') || 
         url.includes('api.airtable.com') || 
         url.includes('api.emailjs.com');
}

// Helper function to determine if a request is for a static asset
function isStaticAsset(url) {
  const staticExtensions = [
    '.js', '.css', '.html', '.json', '.svg', '.ico', 
    '.woff', '.woff2', '.ttf', '.otf'
  ];
  
  return staticExtensions.some(ext => url.endsWith(ext)) || 
         STATIC_ASSETS.includes(new URL(url).pathname);
}

// Fetch event - Network first for API requests, Cache first for static assets
self.addEventListener('fetch', (event) => {
  const url = event.request.url;
  
  // Skip cross-origin requests
  if (!url.startsWith(self.location.origin) && !isApiRequest(url)) {
    return;
  }
  
  // For API requests, use network with offline fallback
  if (isApiRequest(url)) {
    event.respondWith(
      fetch(event.request)
        .catch(() => {
          return new Response(
            JSON.stringify({ 
              error: 'You are offline. The request will be processed when you are back online.' 
            }),
            { 
              headers: { 'Content-Type': 'application/json' },
              status: 503
            }
          );
        })
    );
    return;
  }
  
  // For static assets, use cache first strategy
  if (isStaticAsset(url)) {
    event.respondWith(
      caches.match(event.request)
        .then((cacheResponse) => {
          // Return cached response if found
          if (cacheResponse) {
            return cacheResponse;
          }
          
          // Otherwise fetch from network and cache
          return fetch(event.request)
            .then((networkResponse) => {
              if (!networkResponse || networkResponse.status !== 200) {
                return networkResponse;
              }
              
              // Cache the response in the dynamic cache
              const responseToCache = networkResponse.clone();
              caches.open(DYNAMIC_CACHE)
                .then((cache) => {
                  cache.put(event.request, responseToCache);
                });
              
              return networkResponse;
            })
            .catch(() => {
              // For navigation requests, return the offline page
              if (event.request.mode === 'navigate') {
                return caches.match('/offline.html');
              }
              
              return new Response('Not available while offline', { 
                status: 503, 
                headers: { 'Content-Type': 'text/plain' } 
              });
            });
        })
    );
    return;
  }
  
  // For all other requests, try network first then fallback to cache
  event.respondWith(
    fetch(event.request)
      .then((networkResponse) => {
        // Cache valid responses for future offline use
        if (networkResponse && networkResponse.status === 200) {
          const responseToCache = networkResponse.clone();
          caches.open(DYNAMIC_CACHE)
            .then((cache) => {
              cache.put(event.request, responseToCache);
            });
        }
        
        return networkResponse;
      })
      .catch(() => {
        // Try to get from cache if network fails
        return caches.match(event.request)
          .then((cacheResponse) => {
            if (cacheResponse) {
              return cacheResponse;
            }
            
            // For navigation requests, return the offline page
            if (event.request.mode === 'navigate') {
              return caches.match('/offline.html');
            }
            
            return new Response('Not available while offline', { 
              status: 503, 
              headers: { 'Content-Type': 'text/plain' } 
            });
          });
      })
  );
});

// Handle background sync for bookings
self.addEventListener('sync', (event) => {
  if (event.tag === 'sync-bookings') {
    console.log('[Service Worker] Syncing bookings...');
    event.waitUntil(syncBookings());
  }
});

// Function to sync bookings from IndexedDB to server
async function syncBookings() {
  try {
    // Open IndexedDB
    const db = await new Promise((resolve, reject) => {
      const request = indexedDB.open('aphrodite-db', 1);
      request.onerror = reject;
      request.onsuccess = (event) => resolve(event.target.result);
    });
    
    // Get unsynced bookings
    const unsyncedBookings = await new Promise((resolve, reject) => {
      const transaction = db.transaction(['bookings'], 'readonly');
      const store = transaction.objectStore('bookings');
      const index = store.index('synced');
      const request = index.getAll(IDBKeyRange.only(false));
      
      request.onerror = reject;
      request.onsuccess = () => resolve(request.result || []);
    });
    
    if (unsyncedBookings.length === 0) {
      console.log('[Service Worker] No bookings to sync');
      return;
    }
    
    console.log(`[Service Worker] Found ${unsyncedBookings.length} bookings to sync`);
    
    // Process each booking
    for (const booking of unsyncedBookings) {
      try {
        // Send booking to server
        const response = await fetch('/api/bookings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(booking)
        });
        
        if (!response.ok) {
          throw new Error(`Server responded with status ${response.status}`);
        }
        
        console.log(`[Service Worker] Successfully synced booking ${booking.id}`);
        
        // Update booking status in IndexedDB
        await new Promise((resolve, reject) => {
          const transaction = db.transaction(['bookings'], 'readwrite');
          const store = transaction.objectStore('bookings');
          
          // Mark as synced
          booking.synced = true;
          
          const request = store.put(booking);
          request.onerror = reject;
          request.onsuccess = resolve;
        });
      } catch (error) {
        console.error(`[Service Worker] Failed to sync booking ${booking.id}:`, error);
      }
    }
    
    // Notify clients about sync completion
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'SYNC_COMPLETED',
        bookingsCount: unsyncedBookings.length
      });
    });
    
  } catch (error) {
    console.error('[Service Worker] Error during sync:', error);
  }
}

// Handle push notifications
self.addEventListener('push', (event) => {
  if (!event.data) return;
  
  try {
    const data = event.data.json();
    
    const options = {
      body: data.message || 'Nuovo aggiornamento!',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      vibrate: [100, 50, 100],
      data: {
        url: data.url || '/'
      }
    };
    
    event.waitUntil(
      self.registration.showNotification(
        data.title || 'Aphrodite Center Store',
        options
      )
    );
  } catch (error) {
    console.error('[Service Worker] Error handling push notification:', error);
  }
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();
  
  event.waitUntil(
    clients.matchAll({ type: 'window' })
      .then((clientList) => {
        const url = event.notification.data.url || '/';
        
        // Check if there is already a window/tab open with the target URL
        for (const client of clientList) {
          if (client.url === url && 'focus' in client) {
            return client.focus();
          }
        }
        
        // If no window/tab is open or URL doesn't match, open a new one
        if (clients.openWindow) {
          return clients.openWindow(url);
        }
      })
  );
});
