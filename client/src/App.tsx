import { Switch, Route, useLocation } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BookingProvider } from "@/context/booking-context";
import { useEffect, useState } from "react";
import { queryClient } from "./lib/queryClient";

import NotFound from "@/pages/not-found";
import Home from "@/pages/home";
import Servizi from "@/pages/servizi";
import Prenotazione from "@/pages/prenotazione";
import Conferma from "@/pages/conferma";
import Profilo from "@/pages/profilo";
import Info from "@/pages/info";
import TestAirtable from "@/pages/test-airtable";

import Header from "@/components/layout/header";
import Footer from "@/components/layout/footer";
import SidebarNavigation from "@/components/layout/sidebar-navigation";
import ApiKeyModal from "@/components/ui/api-key-modal";
import InstallPrompt from "@/components/ui/install-prompt";

function App() {
  const [location] = useLocation();
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Configurazione manuale - pannello di configurazione disattivato
  useEffect(() => {
    // Imposta delle chiavi di esempio per evitare errori
    if (!localStorage.getItem('airtableApiKey')) {
      localStorage.setItem('airtableApiKey', 'temporary_disabled_key');
    }
    if (!localStorage.getItem('emailjsConfig')) {
      localStorage.setItem('emailjsConfig', JSON.stringify({
        serviceId: 'temporary_service_id',
        templateId: 'temporary_template_id',
        userId: 'temporary_user_id'
      }));
    }
    // Non mostrare il pannello di configurazione
    setShowApiKeyModal(false);
  }, []);

  // Monitor online status
  useEffect(() => {
    const handleOnlineStatus = () => {
      setIsOffline(!navigator.onLine);
    };

    window.addEventListener('online', handleOnlineStatus);
    window.addEventListener('offline', handleOnlineStatus);

    return () => {
      window.removeEventListener('online', handleOnlineStatus);
      window.removeEventListener('offline', handleOnlineStatus);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <BookingProvider>
          <div className="min-h-screen flex flex-col">
            <Header 
              isOffline={isOffline} 
              onMenuToggle={() => setSidebarOpen(true)}
            />
            
            <SidebarNavigation 
              isOpen={sidebarOpen}
              onClose={() => setSidebarOpen(false)}
            />
            
            <main className="flex-grow">
              <div className="container mx-auto px-4 py-6">
                <Switch>
                  <Route path="/" component={Home} />
                  <Route path="/servizi" component={Servizi} />
                  <Route path="/prenotazione" component={Prenotazione} />
                  <Route path="/conferma" component={Conferma} />
                  <Route path="/profilo" component={Profilo} />
                  <Route path="/info" component={Info} />
                  <Route path="/test-airtable" component={TestAirtable} />
                  <Route component={NotFound} />
                </Switch>
              </div>
            </main>
            
            <Footer />
            
            <Toaster />
            <InstallPrompt />
            
            {showApiKeyModal && (
              <ApiKeyModal onClose={() => setShowApiKeyModal(false)} />
            )}
          </div>
        </BookingProvider>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
