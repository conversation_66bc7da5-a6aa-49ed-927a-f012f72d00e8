@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Palette colori principale */
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --primary: 43 65% 52%; /* Gold color: #d4af37 */
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 20%; /* Dark gray: #333333 */
  --secondary-foreground: 0 0% 98%;
  --accent: 43 61% 73%; /* Gold light: #e9d18b */
  --accent-foreground: 20 14.3% 4.1%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 43 65% 52%;
  --radius: 0.5rem;

  /* Custom variables */
  --color-gold: 43 65% 52%;
  --color-gold-light: 43 61% 73%;
  --color-gold-dark: 43 80% 38%;
  --color-off-white: 0 0% 98%;
  --color-light-gray: 0 0% 94%;
  --color-medium-gray: 0 0% 80%;
  --color-dark-gray: 0 0% 20%;
}

.dark {
  --background: 20 14.3% 4.1%;
  --foreground: 60 9.1% 97.8%;
  --muted: 12 6.5% 15.1%;
  --muted-foreground: 24 5.4% 63.9%;
  --popover: 20 14.3% 4.1%;
  --popover-foreground: 60 9.1% 97.8%;
  --card: 20 14.3% 4.1%;
  --card-foreground: 60 9.1% 97.8%;
  --border: 12 6.5% 15.1%;
  --input: 12 6.5% 15.1%;
  --primary: 43 65% 52%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 20%;
  --secondary-foreground: 0 0% 98%;
  --accent: 43 61% 73%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 43 65% 52%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-off-white font-body text-dark-gray;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-secondary;
  }

  .font-accent {
    font-family: 'Great Vibes', cursive;
  }

  .font-primary {
    font-family: 'Montserrat', sans-serif;
  }

  .font-secondary {
    font-family: 'Playfair Display', serif;
  }

  .font-body {
    font-family: 'Open Sans', sans-serif;
  }
}

@layer components {
  .service-item {
    @apply transition-all duration-300;
  }

  .service-item:hover {
    @apply bg-light-gray;
  }

  .service-item.selected {
    @apply bg-accent;
  }

  .datepicker-day {
    @apply transition-all duration-200;
  }

  .datepicker-day:hover:not(.disabled) {
    @apply bg-accent text-dark-gray;
  }

  .datepicker-day.selected {
    @apply bg-primary text-white;
  }

  .datepicker-day.disabled {
    @apply text-medium-gray cursor-not-allowed;
  }

  .time-slot {
    @apply transition-all duration-200;
  }

  .time-slot:hover:not(.disabled) {
    @apply bg-accent text-dark-gray;
  }

  .time-slot.selected {
    @apply bg-primary text-white;
  }

  .time-slot.disabled {
    @apply text-medium-gray cursor-not-allowed bg-light-gray;
  }

  .view-transition {
    @apply transition-all duration-500;
  }

  .toast {
    animation: slideIn 0.3s ease-in-out;
  }

  @keyframes slideIn {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  .input-focused {
    @apply border-primary ring-2 ring-primary/25;
  }
}

/* Custom utilities */
@layer utilities {
  .bg-gold {
    background-color: hsl(var(--color-gold));
  }
  
  .bg-gold-light {
    background-color: hsl(var(--color-gold-light));
  }
  
  .bg-gold-dark {
    background-color: hsl(var(--color-gold-dark));
  }
  
  .bg-off-white {
    background-color: hsl(var(--color-off-white));
  }
  
  .bg-light-gray {
    background-color: hsl(var(--color-light-gray));
  }
  
  .bg-medium-gray {
    background-color: hsl(var(--color-medium-gray));
  }
  
  .bg-dark-gray {
    background-color: hsl(var(--color-dark-gray));
  }
  
  .text-gold {
    color: hsl(var(--color-gold));
  }
  
  .text-dark-gray {
    color: hsl(var(--color-dark-gray));
  }
  
  .text-medium-gray {
    color: hsl(var(--color-medium-gray));
  }
  
  .border-gold {
    border-color: hsl(var(--color-gold));
  }
  
  .border-gold-light {
    border-color: hsl(var(--color-gold-light));
  }
  
  .border-medium-gray {
    border-color: hsl(var(--color-medium-gray));
  }
  
  .border-light-gray {
    border-color: hsl(var(--color-light-gray));
  }
}
