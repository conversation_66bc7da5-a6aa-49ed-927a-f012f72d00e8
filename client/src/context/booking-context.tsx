import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useToast } from "@/hooks/use-toast";
import { generateId } from "@/lib/utils";
import { saveBooking } from "@/lib/indexeddb";
import { queryClient, apiRequest } from "@/lib/queryClient";

// Types
interface Service {
  id: string;
  name: string;
  price: number;
}

interface CustomerInfo {
  nome?: string;
  cognome?: string;
  email?: string;
  telefono?: string;
  operatrice?: string;
  note?: string;
}

interface BookingContextType {
  selectedServices: Service[];
  totalPrice: number;
  selectedDate: string | null;
  selectedTime: string | null;
  customerInfo: CustomerInfo;
  addService: (service: Service) => void;
  removeService: (serviceId: string) => void;
  setSelectedDate: (date: string) => void;
  setSelectedTime: (time: string) => void;
  setCustomerInfo: (info: CustomerInfo) => void;
  submitBooking: () => Promise<void>;
  resetBooking: () => void;
}

// Create context
const BookingContext = createContext<BookingContextType | undefined>(undefined);

// Provider component
export function BookingProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const [selectedServices, setSelectedServices] = useState<Service[]>([]);
  const [totalPrice, setTotalPrice] = useState(0);
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({});
  
  // Calculate total price when services change
  useEffect(() => {
    const newTotal = selectedServices.reduce((total, service) => total + service.price, 0);
    setTotalPrice(newTotal);
  }, [selectedServices]);
  
  // Load booking data from localStorage
  useEffect(() => {
    const savedBooking = localStorage.getItem('currentBooking');
    if (savedBooking) {
      try {
        const bookingData = JSON.parse(savedBooking);
        setSelectedServices(bookingData.selectedServices || []);
        setSelectedDate(bookingData.selectedDate || null);
        setSelectedTime(bookingData.selectedTime || null);
        setCustomerInfo(bookingData.customerInfo || {});
      } catch (error) {
        console.error("Error loading saved booking:", error);
      }
    }
  }, []);
  
  // Save booking data to localStorage when it changes
  useEffect(() => {
    const bookingData = {
      selectedServices,
      selectedDate,
      selectedTime,
      customerInfo
    };
    
    localStorage.setItem('currentBooking', JSON.stringify(bookingData));
  }, [selectedServices, selectedDate, selectedTime, customerInfo]);
  
  const addService = (service: Service) => {
    setSelectedServices(prev => [...prev, service]);
  };
  
  const removeService = (serviceId: string) => {
    setSelectedServices(prev => prev.filter(service => service.id !== serviceId));
  };
  
  const resetBooking = () => {
    setSelectedServices([]);
    setSelectedDate(null);
    setSelectedTime(null);
    setCustomerInfo({});
    localStorage.removeItem('currentBooking');
  };
  
  const submitBooking = async () => {
    const isOnline = navigator.onLine;
    const bookingId = generateId();
    
    // Prepare booking data
    const bookingData = {
      id: bookingId,
      nome: customerInfo.nome || "",
      cognome: customerInfo.cognome || "",
      email: customerInfo.email || "",
      telefono: customerInfo.telefono || "",
      operatrice: customerInfo.operatrice || "",
      dataAppuntamento: selectedDate || "",
      oraAppuntamento: selectedTime || "",
      servizi: selectedServices,
      prezzoTotale: totalPrice,
      note: customerInfo.note || "",
      createdAt: Date.now(),
      synced: false
    };
    
    try {
      // Save booking to IndexedDB for offline support
      await saveBooking(bookingData);
      
      if (isOnline) {
        // First verify/create client
        await apiRequest('/api/clients/find-or-create', {
          method: 'POST',
          body: JSON.stringify({
            nome: customerInfo.nome,
            cognome: customerInfo.cognome,
            email: customerInfo.email,
            telefono: customerInfo.telefono
          }),
          headers: { 'Content-Type': 'application/json' }
        });
        
        // Then send booking to server
        await apiRequest('/api/bookings', {
          method: 'POST',
          body: JSON.stringify(bookingData),
          headers: { 'Content-Type': 'application/json' }
        });
        
        // Mark as synced in IndexedDB
        bookingData.synced = true;
        await saveBooking(bookingData);
        
        toast({
          title: "Prenotazione confermata",
          description: "La tua prenotazione è stata registrata con successo",
        });
      } else {
        toast({
          title: "Prenotazione salvata offline",
          description: "La prenotazione verrà sincronizzata quando sarai online",
        });
        
        // Try to register a sync event if supported
        if ('serviceWorker' in navigator && 'SyncManager' in window) {
          navigator.serviceWorker.ready
            .then((registration) => {
              // Controllo se sync è disponibile nel registro
              if (registration.sync) {
                return registration.sync.register('sync-bookings');
              }
              console.log('Sync API non supportata in questo browser');
              return Promise.resolve();
            })
            .catch((error) => {
              console.error("Error registering sync:", error);
            });
        }
      }
      
      return Promise.resolve();
    } catch (error) {
      console.error("Error submitting booking:", error);
      toast({
        title: "Errore durante la prenotazione",
        description: "La prenotazione è stata salvata localmente e verrà sincronizzata quando possibile",
        variant: "destructive",
      });
      
      return Promise.reject(error);
    }
  };
  
  return (
    <BookingContext.Provider
      value={{
        selectedServices,
        totalPrice,
        selectedDate,
        selectedTime,
        customerInfo,
        addService,
        removeService,
        setSelectedDate,
        setSelectedTime,
        setCustomerInfo,
        submitBooking,
        resetBooking
      }}
    >
      {children}
    </BookingContext.Provider>
  );
}

// Custom hook to use the booking context
export function useBooking() {
  const context = useContext(BookingContext);
  if (context === undefined) {
    throw new Error("useBooking must be used within a BookingProvider");
  }
  return context;
}
