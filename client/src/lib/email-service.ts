// Email service for sending confirmation emails using EmailJS

interface BookingData {
  id: string;
  nome: string;
  cognome: string;
  email: string;
  telefono: string;
  operatrice: string;
  dataAppuntamento: string;
  oraAppuntamento: string;
  servizi: Array<{ id: string; name: string; price: number; }>;
  prezzoTotale: number;
  note?: string;
}

class EmailService {
  private emailApiUrl: string;
  private serviceId: string;
  private templateId: string;
  private userId: string;
  private isConfigured: boolean;

  constructor() {
    this.emailApiUrl = 'https://api.emailjs.com/api/v1.0/email/send';
    this.serviceId = '';
    this.templateId = '';
    this.userId = '';
    this.isConfigured = false;
    
    // Try to load config from localStorage
    this.loadConfig();
  }

  /**
   * Load configuration from localStorage
   */
  private loadConfig(): void {
    const configString = localStorage.getItem('emailjsConfig');
    if (configString) {
      try {
        const config = JSON.parse(configString);
        this.configure(config);
      } catch (error) {
        console.error('Error parsing EmailJS config:', error);
      }
    }
  }

  /**
   * Configure the email service
   */
  public configure(config: { serviceId: string; templateId: string; userId: string }): void {
    this.serviceId = config.serviceId;
    this.templateId = config.templateId;
    this.userId = config.userId;
    this.isConfigured = !!(this.serviceId && this.templateId && this.userId);
    
    if (this.isConfigured) {
      console.log('Email Service configurato con successo');
    } else {
      console.warn('Email Service configurazione incompleta');
    }
  }

  /**
   * Check if the service is configured
   */
  private checkConfiguration(): boolean {
    if (!this.isConfigured) {
      console.warn('Email Service non configurato. Utilizzare il metodo configure() prima di inviare email.');
      return false;
    }
    return true;
  }

  /**
   * Send confirmation email to customer
   */
  public async sendConfirmationEmail(bookingData: BookingData): Promise<boolean> {
    if (!this.checkConfiguration()) {
      return Promise.reject(new Error('Email Service non configurato'));
    }

    try {
      // Prepare template params
      const templateParams = {
        to_name: `${bookingData.nome} ${bookingData.cognome}`,
        to_email: bookingData.email,
        booking_date: bookingData.dataAppuntamento,
        booking_time: bookingData.oraAppuntamento,
        services: bookingData.servizi.map(s => s.name).join(', '),
        total_price: `€${bookingData.prezzoTotale.toFixed(2)}`,
        operator: bookingData.operatrice
      };

      // Make API call
      const response = await fetch(this.emailApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          service_id: this.serviceId,
          template_id: this.templateId,
          user_id: this.userId,
          template_params: templateParams
        })
      });

      // Check response
      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Errore invio email: ${errorData || 'Errore sconosciuto'}`);
      }

      console.log('Email di conferma inviata con successo');
      return true;
    } catch (error) {
      console.error('Errore durante l\'invio dell\'email:', error);
      throw error;
    }
  }

  /**
   * Send notification email to salon
   */
  public async sendNotificationEmail(bookingData: BookingData): Promise<boolean> {
    if (!this.checkConfiguration()) {
      return Promise.reject(new Error('Email Service non configurato'));
    }

    try {
      // Prepare template params
      const templateParams = {
        client_name: `${bookingData.nome} ${bookingData.cognome}`,
        client_email: bookingData.email,
        client_phone: bookingData.telefono,
        booking_date: bookingData.dataAppuntamento,
        booking_time: bookingData.oraAppuntamento,
        services: bookingData.servizi.map(s => s.name).join(', '),
        total_price: `€${bookingData.prezzoTotale.toFixed(2)}`,
        operator: bookingData.operatrice,
        notes: bookingData.note || 'Nessuna nota'
      };

      // Make API call
      const response = await fetch(this.emailApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          service_id: this.serviceId,
          template_id: `${this.templateId}_notification`, // Different template for notification
          user_id: this.userId,
          template_params: templateParams
        })
      });

      // Check response
      if (!response.ok) {
        const errorData = await response.text();
        throw new Error(`Errore invio email di notifica: ${errorData || 'Errore sconosciuto'}`);
      }

      console.log('Email di notifica inviata con successo');
      return true;
    } catch (error) {
      console.error('Errore durante l\'invio dell\'email di notifica:', error);
      throw error;
    }
  }
}

// Create and export service instance
const emailService = new EmailService();

/**
 * Send confirmation email for a booking
 */
export async function sendConfirmationEmail(bookingData: BookingData): Promise<boolean> {
  try {
    // Send email to customer
    await emailService.sendConfirmationEmail(bookingData);
    
    // Send notification to salon
    await emailService.sendNotificationEmail(bookingData);
    
    return true;
  } catch (error) {
    console.error('Error sending emails:', error);
    
    // We don't want to fail the booking process if email sending fails
    return false;
  }
}

export default emailService;
