/**
 * IndexedDB Service - Handles local storage for offline functionality
 */

// Define the database name and version
const DB_NAME = 'aphrodite-db';
const DB_VERSION = 1;
const STORE_NAME = 'bookings';

// Booking type definition
interface StoredBooking {
  id: string;
  nome: string;
  cognome: string;
  email: string;
  telefono: string;
  operatrice: string;
  dataAppuntamento: string;
  oraAppuntamento: string;
  servizi: Array<{ id: string; name: string; price: number; }>;
  prezzoTotale: number;
  note?: string;
  createdAt: number;
  synced: boolean;
}

/**
 * Opens a connection to the IndexedDB database
 * @returns Promise that resolves to the database connection
 */
async function openDatabase(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(DB_NAME, DB_VERSION);
    
    request.onerror = (event) => {
      console.error('Error opening IndexedDB:', event);
      reject('Could not open IndexedDB');
    };
    
    request.onsuccess = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      resolve(db);
    };
    
    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      
      // Create object store for bookings if it doesn't exist
      if (!db.objectStoreNames.contains(STORE_NAME)) {
        const store = db.createObjectStore(STORE_NAME, { keyPath: 'id' });
        
        // Create indexes
        store.createIndex('synced', 'synced', { unique: false });
        store.createIndex('createdAt', 'createdAt', { unique: false });
        
        console.log('IndexedDB store created');
      }
    };
  });
}

/**
 * Saves a booking to IndexedDB
 * @param booking The booking data to save
 * @returns Promise that resolves when the booking is saved
 */
export async function saveBooking(booking: StoredBooking): Promise<void> {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);
    
    const request = store.put(booking);
    
    request.onsuccess = () => {
      console.log('Booking saved to IndexedDB:', booking.id);
      resolve();
    };
    
    request.onerror = (event) => {
      console.error('Error saving booking to IndexedDB:', event);
      reject('Could not save booking');
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
}

/**
 * Gets a booking from IndexedDB by ID
 * @param id The ID of the booking to retrieve
 * @returns Promise that resolves to the booking or null if not found
 */
export async function getBooking(id: string): Promise<StoredBooking | null> {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readonly');
    const store = transaction.objectStore(STORE_NAME);
    
    const request = store.get(id);
    
    request.onsuccess = () => {
      resolve(request.result || null);
    };
    
    request.onerror = (event) => {
      console.error('Error getting booking from IndexedDB:', event);
      reject('Could not get booking');
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
}

/**
 * Gets all bookings from IndexedDB
 * @returns Promise that resolves to an array of bookings
 */
export async function getStoredBookings(): Promise<StoredBooking[]> {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readonly');
    const store = transaction.objectStore(STORE_NAME);
    
    const request = store.getAll();
    
    request.onsuccess = () => {
      resolve(request.result || []);
    };
    
    request.onerror = (event) => {
      console.error('Error getting bookings from IndexedDB:', event);
      reject('Could not get bookings');
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
}

/**
 * Updates the synced status of a booking
 * @param id The ID of the booking to update
 * @param synced The new synced status
 * @returns Promise that resolves when the booking is updated
 */
export async function updateBookingSyncStatus(id: string, synced: boolean): Promise<void> {
  const booking = await getBooking(id);
  
  if (!booking) {
    throw new Error(`Booking with ID ${id} not found`);
  }
  
  booking.synced = synced;
  return saveBooking(booking);
}

/**
 * Gets all unsynced bookings from IndexedDB
 * @returns Promise that resolves to an array of unsynced bookings
 */
export async function getUnsyncedBookings(): Promise<StoredBooking[]> {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readonly');
    const store = transaction.objectStore(STORE_NAME);
    const index = store.index('synced');
    
    const request = index.getAll(IDBKeyRange.only(false));
    
    request.onsuccess = () => {
      resolve(request.result || []);
    };
    
    request.onerror = (event) => {
      console.error('Error getting unsynced bookings from IndexedDB:', event);
      reject('Could not get unsynced bookings');
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
}

/**
 * Deletes a booking from IndexedDB
 * @param id The ID of the booking to delete
 * @returns Promise that resolves when the booking is deleted
 */
export async function deleteBooking(id: string): Promise<void> {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);
    
    const request = store.delete(id);
    
    request.onsuccess = () => {
      console.log('Booking deleted from IndexedDB:', id);
      resolve();
    };
    
    request.onerror = (event) => {
      console.error('Error deleting booking from IndexedDB:', event);
      reject('Could not delete booking');
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
}

/**
 * Clears all bookings from IndexedDB
 * @returns Promise that resolves when all bookings are cleared
 */
export async function clearBookings(): Promise<void> {
  const db = await openDatabase();
  
  return new Promise((resolve, reject) => {
    const transaction = db.transaction([STORE_NAME], 'readwrite');
    const store = transaction.objectStore(STORE_NAME);
    
    const request = store.clear();
    
    request.onsuccess = () => {
      console.log('All bookings cleared from IndexedDB');
      resolve();
    };
    
    request.onerror = (event) => {
      console.error('Error clearing bookings from IndexedDB:', event);
      reject('Could not clear bookings');
    };
    
    transaction.oncomplete = () => {
      db.close();
    };
  });
}
