import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { format, addMonths, addDays, isValid, parse } from "date-fns";
import { it } from "date-fns/locale";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Format date to Italian locale
export function formatDate(date: Date): string {
  if (!isValid(date)) return "";
  return format(date, "EEEE, d MMMM yyyy", { locale: it });
}

// Format time
export function formatTime(time: string): string {
  return time;
}

// Format price in Euro
export function formatPrice(price: number): string {
  return `€${price.toFixed(2)}`;
}

// Generate available dates (excluding past dates and Sundays)
export function getAvailableDates(startDate: Date = new Date()): Date[] {
  const dates: Date[] = [];
  const endDate = addMonths(startDate, 2); // Allow booking up to 2 months in advance
  
  let currentDate = startDate;
  
  // Reset hours to start of day
  currentDate.setHours(0, 0, 0, 0);
  
  while (currentDate <= endDate) {
    // Skip past dates and Sundays (0 = Sunday, 6 = Saturday)
    if (currentDate >= startDate && currentDate.getDay() !== 0) {
      dates.push(new Date(currentDate));
    }
    
    currentDate = addDays(currentDate, 1);
  }
  
  return dates;
}

// Generate available time slots
export function getAvailableTimeSlots(): string[] {
  return [
    "09:00", "09:30", "10:00", "10:30", "11:00", "11:30", 
    "15:00", "15:30", "16:00", "16:30", "17:00", "17:30", "18:00", "18:30"
  ];
}

// Parse Italian date string to Date object
export function parseItalianDate(dateString: string): Date | null {
  try {
    const date = parse(dateString, "EEEE, d MMMM yyyy", new Date(), { locale: it });
    return isValid(date) ? date : null;
  } catch (error) {
    console.error("Error parsing date:", error);
    return null;
  }
}

// Generate a unique ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// Check if the app is running in standalone mode (installed PWA)
export function isRunningAsStandalone(): boolean {
  return window.matchMedia('(display-mode: standalone)').matches || 
         (window.navigator as any).standalone === true;
}

// Validate email format
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// Validate phone number format (Italian)
export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^(\+39)?[0-9]{9,10}$/;
  return phoneRegex.test(phone);
}
