import { Link } from "wouter";

export default function Footer() {
  return (
    <footer className="bg-dark-gray text-white pt-12 pb-6 mt-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <div className="mb-4">
              <span className="font-accent text-2xl text-gold">Aphrodite</span>
              <span className="font-primary text-sm ml-1 text-white">Center Store</span>
            </div>
            <p className="mb-4 text-sm">
              Il tuo centro di bellezza e benessere, dove la cura e la bellezza si incontrano per un'esperienza indimenticabile.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-white hover:text-gold transition-colors duration-300">
                <i className="fab fa-facebook-f"></i>
              </a>
              <a href="#" className="text-white hover:text-gold transition-colors duration-300">
                <i className="fab fa-instagram"></i>
              </a>
              <a href="#" className="text-white hover:text-gold transition-colors duration-300">
                <i className="fab fa-tiktok"></i>
              </a>
            </div>
          </div>
          
          <div>
            <h4 className="font-secondary text-lg text-gold mb-4">Contatti</h4>
            <p className="flex items-start mb-2">
              <i className="fas fa-map-marker-alt text-gold mr-2 mt-1"></i>
              <span>Via della Bellezza 123,<br />00100 Roma, Italia</span>
            </p>
            <p className="flex items-center mb-2">
              <i className="fas fa-phone-alt text-gold mr-2"></i>
              <a href="tel:+391234567890" className="hover:text-gold transition-colors duration-300">
                +39 ************
              </a>
            </p>
            <p className="flex items-center mb-2">
              <i className="fas fa-envelope text-gold mr-2"></i>
              <a href="mailto:<EMAIL>" className="hover:text-gold transition-colors duration-300">
                <EMAIL>
              </a>
            </p>
          </div>
          
          <div>
            <h4 className="font-secondary text-lg text-gold mb-4">Orari</h4>
            <ul className="space-y-2">
              <li className="flex justify-between">
                <span>Lunedì - Venerdì:</span>
                <span>9:00 - 19:00</span>
              </li>
              <li className="flex justify-between">
                <span>Sabato:</span>
                <span>9:00 - 13:00</span>
              </li>
              <li className="flex justify-between">
                <span>Domenica:</span>
                <span>Chiuso</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-8 pt-6 text-center text-sm">
          <p>&copy; {new Date().getFullYear()} Aphrodite Center Store. Tutti i diritti riservati.</p>
          <div className="mt-2 space-x-4">
            <Link href="/info">
              <a className="text-white hover:text-gold">Informazioni</a>
            </Link>
            <Link href="/info">
              <a className="text-white hover:text-gold">Privacy Policy</a>
            </Link>
            <Link href="/info">
              <a className="text-white hover:text-gold">Termini e Condizioni</a>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
