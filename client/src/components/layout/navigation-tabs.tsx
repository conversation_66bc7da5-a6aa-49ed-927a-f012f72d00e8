import { useLocation, <PERSON> } from "wouter";
import { cn } from "@/lib/utils";

export default function NavigationTabs() {
  const [location] = useLocation();

  const isActive = (path: string) => {
    return location === path;
  };

  return (
    <div className="bg-white border-b border-medium-gray">
      <div className="container mx-auto px-4">
        <nav className="flex overflow-x-auto scrollbar-hidden">
          <Link href="/servizi">
            <a className={cn(
              "px-5 py-3 font-primary font-semibold text-sm border-b-2 whitespace-nowrap",
              isActive("/servizi") 
                ? "border-gold text-gold" 
                : "border-transparent text-dark-gray hover:text-gold hover:border-gold-light"
            )}>
              <i className="fas fa-list-ul mr-1"></i> Servizi
            </a>
          </Link>
          
          <Link href="/prenotazione">
            <a className={cn(
              "px-5 py-3 font-primary font-semibold text-sm border-b-2 whitespace-nowrap",
              isActive("/prenotazione") 
                ? "border-gold text-gold" 
                : "border-transparent text-dark-gray hover:text-gold hover:border-gold-light"
            )}>
              <i className="far fa-calendar-alt mr-1"></i> Prenota
            </a>
          </Link>
          
          <Link href="/profilo">
            <a className={cn(
              "px-5 py-3 font-primary font-semibold text-sm border-b-2 whitespace-nowrap",
              isActive("/profilo") 
                ? "border-gold text-gold" 
                : "border-transparent text-dark-gray hover:text-gold hover:border-gold-light"
            )}>
              <i className="far fa-user mr-1"></i> Profilo
            </a>
          </Link>
          
          <Link href="/info">
            <a className={cn(
              "px-5 py-3 font-primary font-semibold text-sm border-b-2 whitespace-nowrap",
              isActive("/info") 
                ? "border-gold text-gold" 
                : "border-transparent text-dark-gray hover:text-gold hover:border-gold-light"
            )}>
              <i className="fas fa-info-circle mr-1"></i> Info
            </a>
          </Link>
        </nav>
      </div>
    </div>
  );
}
