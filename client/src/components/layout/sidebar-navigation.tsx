import { useState } from "react";
import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";

interface SidebarNavigationProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SidebarNavigation({ isOpen, onClose }: SidebarNavigationProps) {
  const [location] = useLocation();

  const menuItems = [
    { href: "/", label: "Home", icon: "fas fa-home" },
    { href: "/servizi", label: "Servizi", icon: "fas fa-spa" },
    { href: "/prenotazione", label: "Prenota", icon: "fas fa-calendar-plus" },
    { href: "/profilo", label: "Profilo", icon: "fas fa-user" },
    { href: "/info", label: "Info", icon: "fas fa-info-circle" },
  ];

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div
        className={cn(
          "fixed top-0 left-0 h-full w-80 bg-white shadow-lg z-50 transform transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center">
            <span className="font-accent text-xl text-gold">Aphrodite</span>
            <span className="font-primary text-sm ml-1 text-dark-gray">Center Store</span>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <i className="fas fa-times text-gray-600"></i>
          </button>
        </div>

        {/* Menu Items */}
        <nav className="p-4">
          <ul className="space-y-2">
            {menuItems.map((item) => (
              <li key={item.href}>
                <Link href={item.href}>
                  <div
                    className={cn(
                      "flex items-center p-3 rounded-lg transition-colors cursor-pointer",
                      location === item.href 
                        ? "bg-gold text-white" 
                        : "hover:bg-gray-100 text-gray-700"
                    )}
                    onClick={onClose}
                  >
                    <i className={cn(item.icon, "w-5 mr-3 text-center")}></i>
                    <span className="font-medium">{item.label}</span>
                  </div>
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        {/* Footer */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
          <div className="text-center text-sm text-gray-500">
            <p>Aphrodite Center Store</p>
            <p>Bellezza e benessere</p>
          </div>
        </div>
      </div>
    </>
  );
}