import { Link } from "wouter";

interface HeaderProps {
  isOffline: boolean;
  onMenuToggle: () => void;
}

export default function Header({ isOffline, onMenuToggle }: HeaderProps) {
  return (
    <header className="bg-white shadow-sm sticky top-0 z-30">
      <div className="container mx-auto px-4 py-3">
        <div className="flex justify-between items-center">
          <div className="flex items-center">
            <button
              onClick={onMenuToggle}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors mr-3 md:hidden"
            >
              <i className="fas fa-bars text-gold text-lg"></i>
            </button>
            
            <Link href="/">
              <div className="h-12 flex items-center cursor-pointer">
                <span className="font-accent text-2xl text-gold">Aphrodite</span>
                <span className="font-primary text-sm ml-1 text-dark-gray">Center Store</span>
              </div>
            </Link>
          </div>
          
          {isOffline && (
            <div className="bg-gold-light text-dark-gray px-3 py-1 rounded-full text-xs font-primary flex items-center">
              <i className="fas fa-wifi mr-1"></i> Modalità offline
            </div>
          )}
        </div>
      </div>
    </header>
  );
}
