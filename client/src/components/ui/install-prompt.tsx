import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { usePwaInstall } from "@/hooks/use-pwa-install";

export default function InstallPrompt() {
  const { deferredPrompt, handleInstallClick } = usePwaInstall();
  const [showPrompt, setShowPrompt] = useState(false);
  
  // Show prompt if not already shown and PWA is installable
  useEffect(() => {
    if (deferredPrompt && !localStorage.getItem("installPromptShown")) {
      // Wait a bit before showing the prompt
      const timer = setTimeout(() => {
        setShowPrompt(true);
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [deferredPrompt]);
  
  const handleInstall = () => {
    handleInstallClick();
    hidePrompt();
  };
  
  const hidePrompt = () => {
    setShowPrompt(false);
    localStorage.setItem("installPromptShown", "true");
  };
  
  if (!showPrompt) return null;
  
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white shadow-lg rounded-t-lg p-4 z-40 animate-slideIn">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="font-secondary font-bold text-lg mb-1">Installa l'app</h3>
          <p className="text-sm">Accedi più facilmente e prenota anche offline!</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            variant="ghost"
            className="px-3 py-2 text-dark-gray font-primary text-sm" 
            onClick={hidePrompt}
          >
            Più tardi
          </Button>
          <Button 
            className="px-4 py-2 bg-gold hover:bg-gold-dark text-white rounded font-primary text-sm" 
            onClick={handleInstall}
          >
            Installa
          </Button>
        </div>
      </div>
    </div>
  );
}
