import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

interface ApiKeyModalProps {
  onClose: () => void;
}

export default function ApiKeyModal({ onClose }: ApiKeyModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  
  const handleSave = () => {
    setIsSubmitting(true);
    
    // Imposta dei valori temporanei per consentire all'app di funzionare
    localStorage.setItem('airtableApiKey', 'temp_key_for_test');
    localStorage.setItem('emailjsConfig', JSON.stringify({
      serviceId: 'temp_service_id',
      templateId: 'temp_template_id',
      userId: 'temp_user_id'
    }));
    
    toast({
      title: "Configurazione completata",
      description: "L'app è pronta per essere testata",
    });
    
    // Close modal
    setIsSubmitting(false);
    onClose();
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-lg p-6 max-w-lg w-full mx-4">
        <h3 className="font-secondary text-xl font-bold mb-4">Informazione</h3>
        
        <div className="space-y-4">
          <p className="text-base text-dark-gray">
            L'applicazione è configurata per utilizzare le API sul lato server per maggiore sicurezza.
          </p>
          <p className="text-base text-dark-gray">
            Cliccando su "Continua", verranno impostate alcune chiavi temporanee 
            per consentire il test dell'interfaccia utente.
          </p>
        </div>
        
        <div className="flex justify-end mt-6">
          <Button
            className="bg-gold hover:bg-gold-dark text-white font-primary font-semibold py-2 px-4 rounded-lg transition-colors duration-300"
            onClick={handleSave}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Configurazione..." : "Continua"}
          </Button>
        </div>
      </div>
    </div>
  );
}
