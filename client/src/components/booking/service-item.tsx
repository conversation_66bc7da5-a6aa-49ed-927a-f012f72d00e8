import { cn } from "@/lib/utils";

interface ServiceItemProps {
  service: {
    id: string;
    name: string;
    price: number;
  };
  selected: boolean;
  onToggle: () => void;
}

export default function ServiceItem({ service, selected, onToggle }: ServiceItemProps) {
  return (
    <div
      className={cn(
        "service-item flex justify-between items-center p-3 rounded cursor-pointer border-b border-light-gray",
        selected && "selected"
      )}
      onClick={onToggle}
    >
      <div className="service-name font-primary font-medium">{service.name}</div>
      <div className="service-price font-primary font-bold text-gold">€{service.price.toFixed(2)}</div>
    </div>
  );
}
