import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface TimePickerProps {
  selectedTime: string | null;
  onTimeSelect: (time: string) => void;
  selectedDate: string | null;
}

export default function TimePicker({ selectedTime, onTimeSelect, selectedDate }: TimePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const timepickerRef = useRef<HTMLDivElement>(null);
  
  // Mock time slots - in a real app, these would come from an API based on the selected date
  const timeSlots = [
    { time: "09:00", available: false },
    { time: "09:30", available: false },
    { time: "10:00", available: true },
    { time: "10:30", available: true },
    { time: "11:00", available: true },
    { time: "11:30", available: true },
    { time: "15:00", available: true },
    { time: "15:30", available: false },
    { time: "16:00", available: true },
    { time: "16:30", available: true },
    { time: "17:00", available: true },
    { time: "17:30", available: true },
    { time: "18:00", available: true },
    { time: "18:30", available: true },
  ];
  
  // Close timepicker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (timepickerRef.current && !timepickerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  
  const toggleTimepicker = () => {
    if (!selectedDate) {
      return;
    }
    setIsOpen(!isOpen);
  };
  
  const handleSelectTime = (time: string) => {
    onTimeSelect(time);
    setIsOpen(false);
  };
  
  return (
    <div className="relative" ref={timepickerRef}>
      <div 
        className={cn(
          "flex items-center border border-medium-gray rounded-lg p-3",
          selectedDate ? "cursor-pointer" : "cursor-not-allowed bg-light-gray"
        )}
        onClick={toggleTimepicker}
      >
        <i className="far fa-clock text-gold mr-2"></i>
        <span>{selectedTime || "Seleziona un orario"}</span>
      </div>
      
      {isOpen && (
        <div className="absolute mt-2 bg-white border border-medium-gray rounded-lg shadow-lg p-4 z-10 w-full md:w-auto min-w-[300px]">
          <div className="font-primary font-semibold mb-3 text-center">Orari disponibili</div>
          <div className="grid grid-cols-3 gap-2">
            {timeSlots.map((slot, index) => (
              <Button
                key={index}
                variant="outline"
                className={cn(
                  "time-slot p-2 text-center rounded text-sm",
                  !slot.available && "disabled",
                  selectedTime === slot.time && "selected"
                )}
                disabled={!slot.available}
                onClick={() => slot.available && handleSelectTime(slot.time)}
              >
                {slot.time}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
