import { useState, useRef, useEffect } from "react";
import { format, addMonths, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isSameMonth, isAfter, isBefore, getDay } from "date-fns";
import { it } from "date-fns/locale";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface DatePickerProps {
  selectedDate: string | null;
  onDateSelect: (dateString: string) => void;
}

export default function DatePicker({ selectedDate, onDateSelect }: DatePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const datepickerRef = useRef<HTMLDivElement>(null);
  
  // Close datepicker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (datepickerRef.current && !datepickerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  
  const toggleDatepicker = () => {
    setIsOpen(!isOpen);
  };
  
  const handlePrevMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  };
  
  const handleNextMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  };
  
  const handleSelectDate = (date: Date) => {
    const formattedDate = format(date, "EEEE, d MMMM yyyy", { locale: it });
    onDateSelect(formattedDate);
    setIsOpen(false);
  };
  
  // Generate days for the calendar
  const daysInMonth = () => {
    const start = startOfMonth(currentMonth);
    const end = endOfMonth(currentMonth);
    return eachDayOfInterval({ start, end });
  };
  
  // Get day of week (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = () => {
    const firstDay = getDay(startOfMonth(currentMonth));
    // Convert to Monday-based (0 = Monday, 6 = Sunday)
    return firstDay === 0 ? 6 : firstDay - 1;
  };
  
  // Check if date is in the past
  const isPastDate = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return isBefore(date, today);
  };
  
  // Check if date is a Sunday
  const isSunday = (date: Date) => {
    return getDay(date) === 0;
  };
  
  // Check if date is disabled
  const isDisabledDate = (date: Date) => {
    return isPastDate(date) || isSunday(date);
  };
  
  // Check if date is selected
  const isSelectedDate = (date: Date) => {
    if (!selectedDate) return false;
    
    try {
      // Parse the Italian formatted date
      const parts = selectedDate.split(", ")[1].split(" ");
      const day = parseInt(parts[0], 10);
      const months = ["gennaio", "febbraio", "marzo", "aprile", "maggio", "giugno", "luglio", "agosto", "settembre", "ottobre", "novembre", "dicembre"];
      const month = months.indexOf(parts[1].toLowerCase());
      const year = parseInt(parts[2], 10);
      
      const selectedDateObj = new Date(year, month, day);
      return isSameDay(date, selectedDateObj);
    } catch (error) {
      console.error("Error parsing selected date:", error);
      return false;
    }
  };
  
  const days = daysInMonth();
  const firstDayOfMonth = getFirstDayOfMonth();
  const monthName = format(currentMonth, "MMMM yyyy", { locale: it });
  
  return (
    <div className="relative" ref={datepickerRef}>
      <div 
        className="flex items-center border border-medium-gray rounded-lg p-3 cursor-pointer"
        onClick={toggleDatepicker}
      >
        <i className="far fa-calendar-alt text-gold mr-2"></i>
        <span>{selectedDate || "Seleziona una data"}</span>
      </div>
      
      {isOpen && (
        <div className="absolute mt-2 bg-white border border-medium-gray rounded-lg shadow-lg p-4 z-10 w-full md:w-auto min-w-[300px]">
          <div className="flex justify-between items-center mb-4">
            <Button 
              variant="ghost" 
              size="icon"
              className="text-gold hover:text-gold-dark" 
              onClick={handlePrevMonth}
            >
              <i className="fas fa-chevron-left"></i>
            </Button>
            <div className="font-primary font-semibold capitalize">{monthName}</div>
            <Button 
              variant="ghost" 
              size="icon"
              className="text-gold hover:text-gold-dark" 
              onClick={handleNextMonth}
            >
              <i className="fas fa-chevron-right"></i>
            </Button>
          </div>
          
          <div className="grid grid-cols-7 gap-1 text-center mb-2">
            <div className="font-primary text-xs text-dark-gray">L</div>
            <div className="font-primary text-xs text-dark-gray">M</div>
            <div className="font-primary text-xs text-dark-gray">M</div>
            <div className="font-primary text-xs text-dark-gray">G</div>
            <div className="font-primary text-xs text-dark-gray">V</div>
            <div className="font-primary text-xs text-dark-gray">S</div>
            <div className="font-primary text-xs text-dark-gray">D</div>
          </div>
          
          <div className="grid grid-cols-7 gap-1 text-center">
            {/* Empty cells for days before the first day of month */}
            {Array.from({ length: firstDayOfMonth }).map((_, index) => (
              <div key={`empty-${index}`}></div>
            ))}
            
            {/* Days of the month */}
            {days.map((day, index) => {
              const isDisabled = isDisabledDate(day);
              const isSelected = isSelectedDate(day);
              
              return (
                <div
                  key={index}
                  className={cn(
                    "datepicker-day h-8 w-8 rounded-full flex items-center justify-center text-sm",
                    isDisabled ? "disabled" : "cursor-pointer",
                    isSelected && "selected"
                  )}
                  onClick={() => !isDisabled && handleSelectDate(day)}
                >
                  {format(day, "d")}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
