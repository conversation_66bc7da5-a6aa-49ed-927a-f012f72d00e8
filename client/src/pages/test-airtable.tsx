import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { apiRequest } from "@/lib/queryClient";

export default function TestAirtable() {
  const [testResult, setTestResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [appointmentsTest, setAppointmentsTest] = useState<any>(null);

  const testConnection = async () => {
    setLoading(true);
    try {
      const result = await apiRequest('/api/test/airtable');
      setTestResult(result);
    } catch (error) {
      setTestResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testBookingCreation = async () => {
    setLoading(true);
    try {
      const testBooking = {
        nome: "<PERSON>",
        cognome: "<PERSON>", 
        email: "<EMAIL>",
        operatrice: "<PERSON>",
        dataAppuntamento: "2025-05-25",
        oraAppuntamento: "10:00"
      };

      const result = await apiRequest('/api/bookings', {
        method: 'POST',
        body: JSON.stringify(testBooking),
        headers: {
          'Content-Type': 'application/json'
        }
      });

      setTestResult({ success: true, message: "Prenotazione test creata con stato RICHIESTO", data: result });
    } catch (error) {
      setTestResult({ success: false, error: (error as Error).message });
    } finally {
      setLoading(false);
    }
  };

  const testTimeSlots = async () => {
    setLoading(true);
    try {
      const result = await apiRequest('/api/time-slots?date=2024-12-01');
      setTestResult({ success: true, message: "Orari disponibili recuperati", data: result });
    } catch (error) {
      setTestResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testClientAppointments = async () => {
    setLoading(true);
    try {
      const result = await apiRequest('/api/appointments/client/1234567890');
      setAppointmentsTest(result);
    } catch (error) {
      setAppointmentsTest({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>Test Airtable Integration</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button 
              onClick={testConnection} 
              disabled={loading}
              className="w-full"
            >
              {loading ? "Testing..." : "Test Connessione"}
            </Button>
            
            <Button 
              onClick={testTimeSlots} 
              disabled={loading}
              variant="outline"
              className="w-full"
            >
              {loading ? "Testing..." : "Test Orari Disponibili"}
            </Button>
            
            <Button 
              onClick={testBookingCreation} 
              disabled={loading}
              variant="outline"
              className="w-full"
            >
              {loading ? "Testing..." : "Test Creazione Prenotazione"}
            </Button>
            
            <Button 
              onClick={testClientAppointments} 
              disabled={loading}
              variant="outline"
              className="w-full"
            >
              {loading ? "Testing..." : "Test Prenotazioni Cliente"}
            </Button>
          </div>

          {testResult && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className={testResult.success ? "text-green-600" : "text-red-600"}>
                  Risultato Test
                </CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="whitespace-pre-wrap text-sm bg-gray-100 p-4 rounded">
                  {JSON.stringify(testResult, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}

          {appointmentsTest && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle>Prenotazioni Cliente Test</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="whitespace-pre-wrap text-sm bg-gray-100 p-4 rounded">
                  {JSON.stringify(appointmentsTest, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}