import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import ServiceItem from "@/components/booking/service-item";
import { useToast } from "@/hooks/use-toast";
import { useBooking } from "@/context/booking-context";
import { formatPrice } from "@/lib/utils";
import { apiRequest } from "@/lib/queryClient";

// Add function to fetch services with Airtable IDs
const fetchServicesWithIds = async (): Promise<any[]> => {
  try {
    const response = await apiRequest('/api/services');
    return Array.isArray(response) ? response : [];
  } catch (error) {
    console.error("Error fetching services:", error);
    return [];
  }
};

export default function Servizi() {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const { selectedServices, totalPrice, addService, removeService } = useBooking();
  const [services, setServices] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);

  useEffect(() => {
    document.title = "I Nostri Servizi - Aphrodite Center Store";

    // Fetch services from static JSON
    fetchServicesWithIds().then(data => {
      if (data && data.length > 0) {
        setServices(data);
      }
    }).catch(error => {
      console.error('Error in fetchServicesWithIds:', error);
    });
  }, []);

  const handleToggleService = (service: { id: string; name: string; price: number }) => {
    const isSelected = selectedServices.some(s => s.id === service.id);

    if (isSelected) {
      removeService(service.id);
    } else {
      addService(service);
      toast({
        title: "Servizio aggiunto",
        description: `${service.name} è stato aggiunto alla selezione`,
        duration: 3000,
      });
    }
  };

  const handleProceedToBooking = () => {
    if (selectedServices.length === 0) {
      toast({
        title: "Nessun servizio selezionato",
        description: "Seleziona almeno un servizio per procedere",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    setLocation("/prenotazione");
  };

  return (
    <div className="view-transition">
      {/* Header Section */}
      <section className="mb-8">
        <h1 className="font-secondary text-3xl md:text-4xl font-bold text-center text-dark-gray mb-2">I Nostri Servizi</h1>
        <p className="font-primary text-center text-dark-gray opacity-80 mb-6">Scegli il tuo trattamento di bellezza</p>

        {/* A panoramic view of a luxury spa interior */}
        <div className="rounded-xl overflow-hidden shadow-lg mb-6 max-h-64">
          <img
            src="https://images.unsplash.com/photo-1540555700478-4be289fbecef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80"
            alt="Aphrodite Center Store interior"
            className="w-full h-full object-cover"
          />
        </div>

        <p className="text-center mb-4">
          Seleziona uno o più servizi per procedere con la prenotazione.<br />
          Tutti i nostri trattamenti sono personalizzabili in base alle tue esigenze.
        </p>
      </section>

      {/* Services Selection */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <h3 className="font-secondary text-xl text-gold mb-4 pb-2 border-b border-gold-light">
            Tutti i Servizi
          </h3>
          <div className="space-y-2 mb-8">
            {services.length === 0 ? (
              <p className="text-center text-muted-foreground">Caricamento servizi...</p>
            ) : (
              services.map((service: any) => (
                <ServiceItem
                  key={service.id}
                  service={service}
                  selected={selectedServices.some(s => s.id === service.id)}
                  onToggle={() => handleToggleService(service)}
                />
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Selected Services Summary */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <h3 className="font-secondary text-xl text-dark-gray mb-4">Servizi selezionati</h3>

          {selectedServices.length > 0 ? (
            <div className="space-y-3 mb-6">
              {selectedServices.map((service) => (
                <div key={service.id} className="flex justify-between items-center p-2 bg-light-gray rounded">
                  <div className="font-primary">{service.name}</div>
                  <div className="font-primary font-bold text-gold">{formatPrice(service.price)}</div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-center mb-6 text-muted-foreground">
              Nessun servizio selezionato
            </p>
          )}

          <div className="flex justify-between items-center p-3 bg-gold-light rounded-lg mb-6">
            <div className="font-primary font-semibold">Totale</div>
            <div className="font-primary font-bold text-xl">{formatPrice(totalPrice)}</div>
          </div>

          <Button
            className="w-full bg-gold hover:bg-gold-dark text-white font-primary font-semibold py-3 px-6 rounded-lg transition-colors duration-300 flex justify-center items-center"
            onClick={handleProceedToBooking}
          >
            <i className="far fa-calendar-alt mr-2"></i> Procedi alla prenotazione
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
