import { useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

export default function Info() {
  useEffect(() => {
    document.title = "Informazioni - Aphrodite Center Store";
  }, []);

  return (
    <div className="view-transition max-w-4xl mx-auto">
      <section className="mb-8">
        <h1 className="font-secondary text-3xl font-bold text-center text-dark-gray mb-2">
          Informazioni
        </h1>
        <p className="font-primary text-center text-dark-gray opacity-80 mb-6">
          Tutto quello che devi sapere su Aphrodite Center Store
        </p>

        <div className="rounded-xl overflow-hidden shadow-lg mb-8">
          <img 
            src="https://images.unsplash.com/photo-1562322140-8baeececf3df?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80" 
            alt="Aphrodite Center Store interior" 
            className="w-full h-full object-cover"
          />
        </div>
      </section>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-10">
        <Card>
          <CardContent className="pt-6">
            <div className="w-14 h-14 bg-gold-light rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-map-marker-alt text-gold text-xl"></i>
            </div>
            <h3 className="font-secondary text-xl font-bold text-center mb-2">Dove siamo</h3>
            <p className="text-center">
              Via della Bellezza 123<br />
              00100 Roma, Italia
            </p>
            <div className="mt-4 text-center">
              <a 
                href="https://maps.google.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gold hover:underline inline-flex items-center"
              >
                <i className="fas fa-directions mr-1"></i> Indicazioni
              </a>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="w-14 h-14 bg-gold-light rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="far fa-clock text-gold text-xl"></i>
            </div>
            <h3 className="font-secondary text-xl font-bold text-center mb-2">Orari</h3>
            <ul className="space-y-2">
              <li className="flex justify-between">
                <span>Lunedì - Venerdì:</span>
                <span>9:00 - 19:00</span>
              </li>
              <li className="flex justify-between">
                <span>Sabato:</span>
                <span>9:00 - 13:00</span>
              </li>
              <li className="flex justify-between">
                <span>Domenica:</span>
                <span>Chiuso</span>
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="w-14 h-14 bg-gold-light rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-phone-alt text-gold text-xl"></i>
            </div>
            <h3 className="font-secondary text-xl font-bold text-center mb-2">Contatti</h3>
            <ul className="space-y-2">
              <li className="flex items-center">
                <i className="fas fa-phone-alt text-gold mr-2"></i>
                <a href="tel:+391234567890" className="hover:text-gold transition-colors duration-300">
                  +39 ************
                </a>
              </li>
              <li className="flex items-center">
                <i className="fas fa-envelope text-gold mr-2"></i>
                <a href="mailto:<EMAIL>" className="hover:text-gold transition-colors duration-300">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center">
                <i className="fab fa-whatsapp text-gold mr-2"></i>
                <a href="https://wa.me/391234567890" className="hover:text-gold transition-colors duration-300">
                  WhatsApp
                </a>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      <Card className="mb-10">
        <CardContent className="p-6">
          <h3 className="font-secondary text-2xl text-gold mb-6">Domande frequenti</h3>
          
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="item-1">
              <AccordionTrigger className="font-primary font-semibold">
                Come posso prenotare un appuntamento?
              </AccordionTrigger>
              <AccordionContent>
                Puoi prenotare facilmente un appuntamento tramite questa app. Vai alla sezione "Servizi", 
                seleziona i trattamenti desiderati, poi procedi alla prenotazione scegliendo data e ora 
                disponibili. Compila il form con i tuoi dati e conferma la prenotazione.
                Riceverai un'email di conferma con tutti i dettagli.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-2">
              <AccordionTrigger className="font-primary font-semibold">
                Posso modificare o cancellare la mia prenotazione?
              </AccordionTrigger>
              <AccordionContent>
                Sì, puoi modificare o cancellare la tua prenotazione contattandoci telefonicamente 
                al numero +39 ************ o via <NAME_EMAIL> con almeno 24 ore 
                di anticipo. In caso di cancellazione con meno di 24 ore di preavviso, potrebbe 
                essere applicata una penale.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-3">
              <AccordionTrigger className="font-primary font-semibold">
                Quali metodi di pagamento accettate?
              </AccordionTrigger>
              <AccordionContent>
                Accettiamo pagamenti in contanti, carte di credito/debito (Visa, Mastercard, American Express), 
                e pagamenti digitali tramite Apple Pay e Google Pay. Per alcuni trattamenti è richiesto un 
                acconto al momento della prenotazione.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-4">
              <AccordionTrigger className="font-primary font-semibold">
                Come funziona l'app quando sono offline?
              </AccordionTrigger>
              <AccordionContent>
                L'app è progettata per funzionare anche offline. Puoi navigare tra i servizi, compilare
                il form di prenotazione e salvare la tua prenotazione anche senza connessione internet.
                Le prenotazioni effettuate offline verranno sincronizzate automaticamente quando tornerai online.
              </AccordionContent>
            </AccordionItem>
            
            <AccordionItem value="item-5">
              <AccordionTrigger className="font-primary font-semibold">
                Quali prodotti utilizzate nei trattamenti?
              </AccordionTrigger>
              <AccordionContent>
                Utilizziamo esclusivamente prodotti di alta qualità, dermatologicamente testati e 
                per la maggior parte naturali. I nostri brand principali includono Dermalogica, 
                Comfort Zone, e la nostra linea esclusiva Aphrodite Essentials. Per allergie o 
                esigenze specifiche, ti invitiamo a comunicarcelo in anticipo.
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </CardContent>
      </Card>

      <div className="rounded-xl overflow-hidden shadow-lg mb-10">
        <img 
          src="https://images.unsplash.com/photo-1600334129128-685c5582fd35?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80" 
          alt="Aphrodite Center Store treatments" 
          className="w-full h-full object-cover"
        />
      </div>

      <Card>
        <CardContent className="p-6">
          <h3 className="font-secondary text-2xl text-gold mb-4">Chi siamo</h3>
          <p className="mb-4">
            Aphrodite Center Store è un centro estetico di eccellenza, nato dalla passione per la bellezza e il benessere.
            Fondato nel 2010, il nostro centro si distingue per l'approccio personalizzato e l'utilizzo di tecniche all'avanguardia.
          </p>
          <p className="mb-4">
            Il nostro team è composto da professionisti qualificati, costantemente aggiornati sulle ultime tendenze e tecnologie
            nel campo dell'estetica e del benessere.
          </p>
          <p>
            La nostra missione è aiutare ogni cliente a sentirsi bella e sicura di sé, offrendo trattamenti personalizzati
            in un ambiente rilassante e accogliente.
          </p>
          
          <div className="flex justify-center space-x-6 mt-6">
            <a href="#" className="text-dark-gray hover:text-gold transition-colors duration-300">
              <i className="fab fa-facebook-f text-xl"></i>
            </a>
            <a href="#" className="text-dark-gray hover:text-gold transition-colors duration-300">
              <i className="fab fa-instagram text-xl"></i>
            </a>
            <a href="#" className="text-dark-gray hover:text-gold transition-colors duration-300">
              <i className="fab fa-tiktok text-xl"></i>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
