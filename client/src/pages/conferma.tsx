import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useBooking } from "@/context/booking-context";
import { formatPrice } from "@/lib/utils";

export default function Conferma() {
  const [_, setLocation] = useLocation();
  const { customerInfo, selectedServices, totalPrice, selectedDate, selectedTime, resetBooking } = useBooking();
  const [syncStatus, setSyncStatus] = useState("Prenotazione confermata");

  useEffect(() => {
    document.title = "Prenotazione Confermata - Aphrodite Center Store";
    
    // Check if booking was synced to Airtable
    const bookingData = localStorage.getItem('currentBooking');
    if (bookingData) {
      const parsed = JSON.parse(bookingData);
      if (parsed.synced) {
        setSyncStatus("Prenotazione sincronizzata con il sistema");
      } else {
        setSyncStatus("Prenotazione salvata localmente. Verrà sincronizzata quando sarai online.");
      }
    }
    
    // Redirect to home if no booking data
    if (!customerInfo.nome || !selectedDate || !selectedTime || selectedServices.length === 0) {
      setLocation("/");
    }
  }, [customerInfo, selectedDate, selectedTime, selectedServices, setLocation]);

  const handleNewBooking = () => {
    resetBooking();
    setLocation("/servizi");
  };

  if (!customerInfo.nome || !selectedDate || !selectedTime) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="view-transition">
      <Card className="text-center mb-8">
        <CardContent className="p-8">
          <div className="w-20 h-20 bg-gold-light rounded-full flex items-center justify-center mx-auto mb-6">
            <i className="fas fa-check text-gold text-3xl"></i>
          </div>
          
          <h2 className="font-secondary text-2xl font-bold text-dark-gray mb-4">Prenotazione confermata!</h2>
          <p className="mb-6">
            Grazie per aver prenotato da Aphrodite Center Store.<br />
            Abbiamo inviato una conferma via email all'indirizzo{" "}
            <span className="font-semibold">{customerInfo.email}</span>.
          </p>
          
          <div className="bg-light-gray rounded-lg p-6 mb-8 mx-auto max-w-md">
            <h3 className="font-secondary text-xl text-gold mb-4">Dettagli appuntamento</h3>
            
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <i className="far fa-calendar-alt text-gold mr-2"></i>
                <span className="font-primary font-medium">Data:</span>
              </div>
              <span className="font-primary">{selectedDate}</span>
            </div>
            
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <i className="far fa-clock text-gold mr-2"></i>
                <span className="font-primary font-medium">Ora:</span>
              </div>
              <span className="font-primary">{selectedTime}</span>
            </div>
            
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <i className="far fa-user text-gold mr-2"></i>
                <span className="font-primary font-medium">Operatrice:</span>
              </div>
              <span className="font-primary">{customerInfo.operatrice}</span>
            </div>
            
            <div className="border-t border-medium-gray my-4"></div>
            
            <div className="space-y-2 mb-4">
              {selectedServices.map((service) => (
                <div key={service.id} className="flex justify-between items-center">
                  <div className="font-primary">{service.name}</div>
                  <div className="font-primary font-bold text-gold">{formatPrice(service.price)}</div>
                </div>
              ))}
            </div>
            
            <div className="flex justify-between items-center p-3 bg-gold-light rounded-lg">
              <div className="font-primary font-semibold">Totale</div>
              <div className="font-primary font-bold text-xl">{formatPrice(totalPrice)}</div>
            </div>
          </div>
          
          <p className="text-sm mb-6">
            Riceverai un promemoria 24 ore prima dell'appuntamento.<br />
            Per modificare o cancellare la prenotazione, contattaci al numero{" "}
            <a href="tel:+391234567890" className="text-gold hover:underline">+39 ************</a>.
          </p>
          
          <Button
            className="bg-gold hover:bg-gold-dark text-white font-primary font-semibold py-3 px-6 rounded-lg transition-colors duration-300 inline-flex items-center"
            onClick={handleNewBooking}
          >
            <i className="fas fa-plus mr-2"></i> Nuova prenotazione
          </Button>
        </CardContent>
      </Card>
      
      {/* A beautiful image of spa treatment elements */}
      <div className="rounded-xl overflow-hidden shadow-lg">
        <img 
          src="https://images.unsplash.com/photo-1507652313519-d4e9174996dd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80" 
          alt="Aphrodite Center Store treatments" 
          className="w-full h-full object-cover"
        />
      </div>
    </div>
  );
}
