import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { formatPrice } from "@/lib/utils";
import { getStoredBookings } from "@/lib/indexeddb";

// Types for booking display
interface StoredBooking {
  id: string;
  nome: string;
  cognome: string;
  email: string;
  telefono: string;
  operatrice: string;
  dataAppuntamento: string;
  oraAppuntamento: string;
  servizi: Array<{ id: string; name: string; price: number; }>;
  prezzoTotale: number;
  createdAt: number;
  synced: boolean;
}

export default function Profilo() {
  const { toast } = useToast();
  const [bookings, setBookings] = useState<StoredBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    document.title = "Il tuo profilo - Aphrodite Center Store";
    loadBookings();
  }, []);

  const loadBookings = async () => {
    try {
      setIsLoading(true);
      const storedBookings = await getStoredBookings();
      setBookings(storedBookings);
    } catch (error) {
      console.error("Error loading bookings:", error);
      toast({
        title: "Errore",
        description: "Impossibile caricare le prenotazioni",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="view-transition max-w-4xl mx-auto">
      <section className="mb-8">
        <h1 className="font-secondary text-3xl font-bold text-center text-dark-gray mb-2">
          Il tuo profilo
        </h1>
        <p className="font-primary text-center text-dark-gray opacity-80 mb-6">
          Gestisci le tue prenotazioni
        </p>
      </section>

      <Card>
        <CardHeader>
          <CardTitle className="font-secondary text-xl text-gold">
            Le tue prenotazioni
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <i className="fas fa-spinner fa-spin text-gold text-2xl mb-4"></i>
              <p>Caricamento prenotazioni...</p>
            </div>
          ) : bookings.length > 0 ? (
            <div className="space-y-4">
              {bookings.map((booking) => (
                <Card key={booking.id} className="overflow-hidden">
                  <div className={`px-3 py-1 text-xs text-white font-semibold ${booking.synced ? 'bg-green-600' : 'bg-amber-500'}`}>
                    {booking.synced ? 'Sincronizzata' : 'In attesa di sincronizzazione'}
                  </div>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h4 className="font-semibold">
                          {booking.nome} {booking.cognome}
                        </h4>
                        <p className="text-sm text-muted-foreground">
                          {booking.email} • {booking.telefono}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">{booking.dataAppuntamento}</div>
                        <div className="text-sm">{booking.oraAppuntamento}</div>
                      </div>
                    </div>
                    
                    <div className="text-sm mb-3">
                      <span className="font-semibold">Operatrice:</span> {booking.operatrice}
                    </div>
                    
                    <div className="text-sm space-y-1 mb-3">
                      <div className="font-semibold">Servizi:</div>
                      {booking.servizi.map((service, idx) => (
                        <div key={idx} className="flex justify-between">
                          <span>{service.name}</span>
                          <span className="font-medium">{formatPrice(service.price)}</span>
                        </div>
                      ))}
                    </div>
                    
                    <div className="flex justify-between font-semibold mt-2 pt-2 border-t">
                      <span>Totale</span>
                      <span className="text-gold">{formatPrice(booking.prezzoTotale)}</span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-light-gray rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="far fa-calendar-alt text-medium-gray text-xl"></i>
              </div>
              <p className="text-muted-foreground">Non hai ancora effettuato prenotazioni</p>
              <Button 
                className="mt-4 bg-gold hover:bg-gold-dark text-white"
                onClick={() => window.location.href = "/servizi"}
              >
                Prenota ora
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}