import { useEffect } from "react";
import { useLocation } from "wouter";
import { Button } from "@/components/ui/button";

export default function Home() {
  const [_, setLocation] = useLocation();

  useEffect(() => {
    document.title = "Aphrodite Center Store - Prenotazione Appuntamenti";
  }, []);

  return (
    <div className="max-w-4xl mx-auto text-center py-12">
      <h1 className="text-4xl md:text-5xl font-bold text-dark-gray mb-6">
        Benvenuta al <span className="font-accent text-gold">Aphrodite</span> Center Store
      </h1>
      
      <p className="text-lg mb-8">
        Il tuo centro di bellezza e benessere, dove la cura e la bellezza si incontrano per un'esperienza indimenticabile.
      </p>
      
      <div className="rounded-xl overflow-hidden shadow-lg mb-10 max-h-80">
        <img 
          src="https://images.unsplash.com/photo-1540555700478-4be289fbecef?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80" 
          alt="Aphrodite Center Store interior" 
          className="w-full h-full object-cover"
        />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <div className="bg-white p-6 rounded-xl shadow-md">
          <div className="w-16 h-16 bg-gold-light rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="fas fa-spa text-gold text-xl"></i>
          </div>
          <h3 className="font-secondary text-xl font-bold mb-3">Trattamenti Premium</h3>
          <p>Scopri i nostri trattamenti di bellezza personalizzati per il viso e per il corpo.</p>
        </div>
        
        <div className="bg-white p-6 rounded-xl shadow-md">
          <div className="w-16 h-16 bg-gold-light rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="fas fa-calendar-alt text-gold text-xl"></i>
          </div>
          <h3 className="font-secondary text-xl font-bold mb-3">Prenotazione Facile</h3>
          <p>Prenota il tuo appuntamento online in pochi minuti, anche quando sei offline.</p>
        </div>
        
        <div className="bg-white p-6 rounded-xl shadow-md">
          <div className="w-16 h-16 bg-gold-light rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="fas fa-gem text-gold text-xl"></i>
          </div>
          <h3 className="font-secondary text-xl font-bold mb-3">Esperienza Luxury</h3>
          <p>Rilassati in un ambiente elegante con prodotti di altissima qualità.</p>
        </div>
      </div>
      
      <div className="rounded-xl overflow-hidden shadow-lg mb-10">
        <img 
          src="https://images.unsplash.com/photo-1507652313519-d4e9174996dd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400&q=80" 
          alt="Aphrodite Center Store treatments" 
          className="w-full h-full object-cover"
        />
      </div>
      
      <Button 
        className="bg-gold hover:bg-gold-dark text-white font-primary font-semibold py-3 px-8 rounded-lg transition-colors duration-300 text-lg"
        onClick={() => setLocation("/servizi")}
      >
        <i className="fas fa-list-ul mr-2"></i>
        Scopri i nostri servizi
      </Button>
    </div>
  );
}
