import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useBooking } from "@/context/booking-context";
import DatePicker from "@/components/booking/date-picker";
import TimePicker from "@/components/booking/time-picker";
import { formatPrice, isValidEmail, isValidPhone } from "@/lib/utils";

const operatrici = [
  { id: "op1", name: "<PERSON>" },
  { id: "op2", name: "<PERSON><PERSON>" },
  { id: "op3", name: "<PERSON> Bianchi" },
  { id: "op4", name: "<PERSON>" },
];

// Add client verification function
const verifyClient = async (clientData) => {
  try {
    const response = await apiRequest('/api/clients/find-or-create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(clientData)
    });
    return response;
  } catch (error) {
    console.error("Client verification error:", error);
    throw error;
  }
};

export default function Prenotazione() {
  const [_, setLocation] = useLocation();
  const { toast } = useToast();
  const { selectedServices, totalPrice, selectedDate, selectedTime, 
          setSelectedDate, setSelectedTime, customerInfo, setCustomerInfo, submitBooking } = useBooking();
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  
  useEffect(() => {
    document.title = "Prenota Appuntamento - Aphrodite Center Store";
    
    // Redirect to services page if no services selected
    if (selectedServices.length === 0) {
      setLocation("/servizi");
      toast({
        title: "Nessun servizio selezionato",
        description: "Seleziona almeno un servizio prima di procedere alla prenotazione",
        variant: "destructive",
      });
    }
  }, [selectedServices, setLocation, toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!customerInfo.nome || !customerInfo.cognome || !customerInfo.email || !customerInfo.telefono) {
      toast({
        title: "Compilare tutti i campi",
        description: "Tutti i campi contrassegnati con * sono obbligatori",
        variant: "destructive",
      });
      return;
    }
    
    if (!isValidEmail(customerInfo.email)) {
      toast({
        title: "Email non valida",
        description: "Inserisci un indirizzo email valido",
        variant: "destructive",
      });
      return;
    }
    
    if (!isValidPhone(customerInfo.telefono)) {
      toast({
        title: "Numero di telefono non valido",
        description: "Inserisci un numero di telefono valido",
        variant: "destructive",
      });
      return;
    }
    
    if (!selectedDate || !selectedTime) {
      toast({
        title: "Data e ora mancanti",
        description: "Seleziona una data e un orario per l'appuntamento",
        variant: "destructive",
      });
      return;
    }
    
    if (!customerInfo.operatrice) {
      toast({
        title: "Operatrice non selezionata",
        description: "Seleziona un'operatrice per il tuo appuntamento",
        variant: "destructive",
      });
      return;
    }
    
    if (!privacyAccepted) {
      toast({
        title: "Privacy policy",
        description: "Devi accettare la privacy policy per continuare",
        variant: "destructive",
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      await submitBooking();
      setLocation("/conferma");
    } catch (error) {
      console.error("Error submitting booking:", error);
      toast({
        title: "Errore durante la prenotazione",
        description: "Si è verificato un errore. La prenotazione verrà salvata localmente e sincronizzata quando sarai online.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="view-transition">
      {/* Header Section */}
      <section className="mb-8">
        <h1 className="font-secondary text-3xl font-bold text-center text-dark-gray mb-2">
          Prenota il tuo appuntamento
        </h1>
        <p className="font-primary text-center text-dark-gray opacity-80 mb-6">
          Seleziona data, ora e inserisci i tuoi dati
        </p>
      </section>

      {/* Booking Summary */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <h3 className="font-secondary text-xl text-gold mb-4">Riepilogo servizi</h3>
          
          <div className="space-y-2 mb-4">
            {selectedServices.map((service) => (
              <div key={service.id} className="flex justify-between items-center p-2 bg-light-gray rounded">
                <div className="font-primary">{service.name}</div>
                <div className="font-primary font-bold text-gold">{formatPrice(service.price)}</div>
              </div>
            ))}
          </div>
          
          <div className="flex justify-between items-center p-3 bg-gold-light rounded-lg">
            <div className="font-primary font-semibold">Totale</div>
            <div className="font-primary font-bold text-xl">{formatPrice(totalPrice)}</div>
          </div>
        </CardContent>
      </Card>

      {/* Date & Time Selection */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <h3 className="font-secondary text-xl text-gold mb-4">Data e ora</h3>
          
          {/* Date Picker */}
          <div className="mb-6">
            <Label className="block font-primary font-semibold mb-2">
              Data appuntamento
            </Label>
            <DatePicker 
              selectedDate={selectedDate}
              onDateSelect={setSelectedDate}
            />
          </div>
          
          {/* Time Picker */}
          <div className="mb-4">
            <Label className="block font-primary font-semibold mb-2">
              Ora appuntamento
            </Label>
            <TimePicker
              selectedTime={selectedTime}
              onTimeSelect={setSelectedTime}
              selectedDate={selectedDate}
            />
          </div>
          
          {/* Operator Selection */}
          <div className="mb-4">
            <Label className="block font-primary font-semibold mb-2">
              Operatrice
            </Label>
            <Select 
              value={customerInfo.operatrice || ""}
              onValueChange={(value) => setCustomerInfo({...customerInfo, operatrice: value})}
            >
              <SelectTrigger className="w-full border border-medium-gray rounded-lg p-3">
                <SelectValue placeholder="Seleziona operatrice" />
              </SelectTrigger>
              <SelectContent>
                {operatrici.map((operatrice) => (
                  <SelectItem key={operatrice.id} value={operatrice.name}>
                    {operatrice.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Customer Information Form */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <h3 className="font-secondary text-xl text-gold mb-4">I tuoi dati</h3>
          
          <form onSubmit={handleSubmit}>
            {/* Personal Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <Label className="block font-primary font-semibold mb-1" htmlFor="nome">
                  Nome *
                </Label>
                <Input
                  id="nome"
                  name="nome"
                  className="w-full border border-medium-gray rounded-lg p-3"
                  required
                  placeholder="Il tuo nome"
                  value={customerInfo.nome || ""}
                  onChange={(e) => setCustomerInfo({...customerInfo, nome: e.target.value})}
                />
              </div>
              
              <div>
                <Label className="block font-primary font-semibold mb-1" htmlFor="cognome">
                  Cognome *
                </Label>
                <Input
                  id="cognome"
                  name="cognome"
                  className="w-full border border-medium-gray rounded-lg p-3"
                  required
                  placeholder="Il tuo cognome"
                  value={customerInfo.cognome || ""}
                  onChange={(e) => setCustomerInfo({...customerInfo, cognome: e.target.value})}
                />
              </div>
              
              <div>
                <Label className="block font-primary font-semibold mb-1" htmlFor="email">
                  Email *
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  className="w-full border border-medium-gray rounded-lg p-3"
                  required
                  placeholder="La tua email"
                  value={customerInfo.email || ""}
                  onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                />
              </div>
              
              <div>
                <Label className="block font-primary font-semibold mb-1" htmlFor="telefono">
                  Telefono *
                </Label>
                <Input
                  id="telefono"
                  name="telefono"
                  type="tel"
                  className="w-full border border-medium-gray rounded-lg p-3"
                  required
                  placeholder="Il tuo numero di telefono"
                  value={customerInfo.telefono || ""}
                  onChange={(e) => setCustomerInfo({...customerInfo, telefono: e.target.value})}
                />
              </div>
            </div>
            
            {/* Notes */}
            <div className="mb-6">
              <Label className="block font-primary font-semibold mb-1" htmlFor="note">
                Note
              </Label>
              <Textarea
                id="note"
                name="note"
                className="w-full border border-medium-gray rounded-lg p-3 min-h-[100px]"
                placeholder="Eventuali richieste o note speciali"
                value={customerInfo.note || ""}
                onChange={(e) => setCustomerInfo({...customerInfo, note: e.target.value})}
              />
            </div>
            
            {/* Privacy Consent */}
            <div className="flex items-start mb-6 space-x-2">
              <Checkbox
                id="privacy"
                checked={privacyAccepted}
                onCheckedChange={(checked) => setPrivacyAccepted(checked as boolean)}
                required
              />
              <Label
                htmlFor="privacy"
                className="text-sm leading-tight"
              >
                Acconsento al trattamento dei miei dati personali come descritto nella{" "}
                <a href="#" className="text-gold hover:underline">
                  Privacy Policy
                </a>{" "}
                *
              </Label>
            </div>
            
            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row justify-between gap-4">
              <Button
                type="button"
                variant="outline"
                className="bg-light-gray hover:bg-medium-gray text-dark-gray font-primary font-semibold py-3 px-6 rounded-lg transition-colors duration-300"
                onClick={() => setLocation("/servizi")}
              >
                <i className="fas fa-arrow-left mr-2"></i> Torna ai servizi
              </Button>
              
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-gold hover:bg-gold-dark text-white font-primary font-semibold py-3 px-6 rounded-lg transition-colors duration-300"
              >
                {isSubmitting ? (
                  <span className="flex items-center">
                    <i className="fas fa-spinner fa-spin mr-2"></i> Elaborazione...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <i className="far fa-calendar-check mr-2"></i> Conferma prenotazione
                  </span>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
