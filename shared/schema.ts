import { pgTable, text, serial, integer, boolean, timestamp, decimal, json } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// Services table
export const services = pgTable("services", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  category: text("category").notNull(),
  description: text("description"),
  duration: integer("duration").notNull(), // Duration in minutes
  active: boolean("active").default(true),
});

export const insertServiceSchema = createInsertSchema(services).omit({
  id: true,
});

export type InsertService = z.infer<typeof insertServiceSchema>;
export type Service = typeof services.$inferSelect;

// Service Categories table
export const serviceCategories = pgTable("service_categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
  displayOrder: integer("display_order").default(0),
});

export const insertServiceCategorySchema = createInsertSchema(serviceCategories).omit({
  id: true,
});

export type InsertServiceCategory = z.infer<typeof insertServiceCategorySchema>;
export type ServiceCategory = typeof serviceCategories.$inferSelect;

// Staff (Operators) table
export const staff = pgTable("staff", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  title: text("title"),
  active: boolean("active").default(true),
});

export const insertStaffSchema = createInsertSchema(staff).omit({
  id: true,
});

export type InsertStaff = z.infer<typeof insertStaffSchema>;
export type Staff = typeof staff.$inferSelect;

// Bookings table
export const bookings = pgTable("bookings", {
  id: text("id").primaryKey(), // Using text for custom UUIDs
  nome: text("nome").notNull(),
  cognome: text("cognome").notNull(),
  email: text("email").notNull(),
  telefono: text("telefono").notNull(),
  operatrice: text("operatrice").notNull(),
  dataAppuntamento: text("data_appuntamento").notNull(),
  oraAppuntamento: text("ora_appuntamento").notNull(),
  servizi: json("servizi").notNull().$type<Array<{ id: string; name: string; price: number; }>>(),
  prezzoTotale: decimal("prezzo_totale", { precision: 10, scale: 2 }).notNull(),
  note: text("note"),
  createdAt: timestamp("created_at").defaultNow(),
  synced: boolean("synced").default(false),
});

// Define a Zod schema for the services array
const serviceItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  price: z.number().positive(),
});

// Create a schema for inserting bookings
export const insertBookingSchema = z.object({
  id: z.string(),
  nome: z.string().min(1, "Il nome è richiesto"),
  cognome: z.string().min(1, "Il cognome è richiesto"),
  email: z.string().email("Email non valida"),
  telefono: z.string().min(5, "Numero di telefono non valido"),
  operatrice: z.string().min(1, "Operatrice è richiesta"),
  dataAppuntamento: z.string().min(1, "Data appuntamento è richiesta"),
  oraAppuntamento: z.string().min(1, "Ora appuntamento è richiesta"),
  servizi: z.array(serviceItemSchema).min(1, "Seleziona almeno un servizio"),
  prezzoTotale: z.number().positive("Il prezzo totale deve essere maggiore di zero"),
  note: z.string().optional(),
  synced: z.boolean().optional(),
});

export type InsertBooking = z.infer<typeof insertBookingSchema>;
export type Booking = typeof bookings.$inferSelect;

// Update the IStorage interface to include the new operations
export interface IStorage {
  // Users
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Bookings
  getBookings(): Promise<Booking[]>;
  getBookingById(id: string): Promise<Booking | undefined>;
  createBooking(booking: InsertBooking): Promise<Booking>;
  updateBooking(id: string, booking: Partial<InsertBooking>): Promise<Booking>;
  deleteBooking(id: string): Promise<void>;
  
  // Services
  getServices(): Promise<Service[]>;
  getServicesByCategory(category: string): Promise<Service[]>;
  getServiceCategories(): Promise<ServiceCategory[]>;
  
  // Time slots
  getAvailableTimeSlots(date: string): Promise<string[]>;
}
