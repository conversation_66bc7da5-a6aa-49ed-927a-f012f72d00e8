# Aphrodite Center Store - Booking Application

## Overview

This repository contains a beauty salon booking application called "Aphrodite Center Store." It's a modern web application built with a React frontend and Express backend, designed to allow customers to browse services, make bookings, and manage their appointments. The application includes offline functionality through PWA capabilities and local storage.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

The application follows a client-server architecture with a clear separation between frontend and backend:

1. **Frontend**: React-based single-page application (SPA) using modern UI components from shadcn/ui
2. **Backend**: Express.js server providing API endpoints
3. **Database**: PostgreSQL database accessed through Drizzle ORM
4. **Data Flow**: Client-side requests handled by server API endpoints that interact with the database

The system is designed as a Progressive Web Application (PWA) with offline capabilities. Data is stored locally using IndexedDB when offline and synced when the connection is restored.

## Key Components

### Frontend

- **React**: Core library for building the UI
- **Wouter**: Lightweight router for navigation
- **Tailwind CSS**: Utility-first CSS framework for styling
- **shadcn/ui**: Component library built on Radix UI primitives
- **React Query**: Data fetching and state management library
- **IndexedDB**: Browser database for offline functionality

### Backend

- **Express.js**: Web server framework handling API requests
- **Drizzle ORM**: Type-safe database toolkit for PostgreSQL
- **Vite**: Development server and build tool

### Database

- **Schema**:
  - Users: Authentication information
  - Services: Beauty salon services information
  - Service Categories: Grouping of services by type
  - Bookings: Customer appointment data

## Data Flow

1. **Service Browsing**: 
   - User browses available beauty services grouped by categories
   - Service data is fetched from the server via API endpoints

2. **Booking Process**:
   - User selects services
   - User picks available date and time
   - User provides personal information
   - System validates and processes booking
   - Confirmation is displayed and email sent

3. **Offline Support**:
   - Bookings created while offline are stored in IndexedDB
   - When connection is restored, pending bookings are synced with the server
   - User can view booking history in the profile section

## External Dependencies

### Frontend Libraries
- Radix UI components for accessible UI elements
- EmailJS for sending confirmation emails
- Tailwind CSS for styling
- React Query for data fetching

### Backend Libraries
- Express for API routing
- Drizzle ORM for database access
- Neon Database serverless PostgreSQL client

### Third-party Services
- EmailJS: For sending confirmation emails
- Airtable: Alternative data storage (configurable)

## Deployment Strategy

The application is deployed as follows:

1. **Build Process**:
   - Frontend: Vite builds the React application into static assets
   - Backend: ESBuild bundles the server code
   - Combined build output is placed in the `dist` directory

2. **Runtime**:
   - Production: `node dist/index.js` serves both the API and static assets
   - Development: Vite dev server with HMR for frontend, with API requests proxied to the Express server

3. **Database**:
   - Uses PostgreSQL database (provided through replit)
   - Database URL configured via environment variables

4. **Progressive Web App**:
   - Service worker for offline functionality
   - Installable to home screen with custom icons and splash screens

## Getting Started

1. Ensure PostgreSQL module is available in the replit
2. Run `npm run dev` to start the development server
3. The application will be available at http://localhost:5000
4. For production, run `npm run build` followed by `npm run start`

## Database Setup

The application uses Drizzle ORM with PostgreSQL. To set up the database:

1. Ensure the `DATABASE_URL` environment variable is set correctly
2. Run `npm run db:push` to push the schema to the database

## Additional Notes

- The application supports Italian language and is designed for a beauty salon business
- API endpoints are prefixed with `/api`
- PWA functionality requires HTTPS in production
- The codebase follows a modular structure with clear separation of concerns